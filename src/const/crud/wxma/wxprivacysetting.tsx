/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.jixiansc.com
 * 注意：
 * 本软件为www.jixiansc.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */

export const tableOption = {
	column: (options) => [
		// {
		//   label: '版本',
		//   prop: "privacy_ver",
		//   type: 'select',
		//   rules: [{
		//     required: true,
		//     message: "请选择版本",
		//     trigger: "blur"
		//   }],
		//   dicData: [{
		//     label: '现网版本',
		//     value: 1
		//   },{
		//     label: '开发版',
		//     value: 2
		//   }],
		//   tip: '说明：\n' +
		//     '\n' +
		//     '1、开发版指的是通过setprivacysetting接口已经配置的用户隐私保护指引内容，但是还没发布到现网，还没正式生效的版本。\n' +
		//     '\n' +
		//     '2、现网版本指的是已经在小程序现网版本已经生效的用户隐私保护指引内容。\n' +
		//     '\n' +
		//     '3、如果小程序已有一个现网版，可以通过该接口（privacy_ver=1）直接修改owner_setting里除了ext_file_media_id之外的{t('wxma.wxprivacysetting.253333-7')}，修改后即可生效。\n' +
		//     '\n' +
		//     '4、如果需要修改其他{t('wxma.wxprivacysetting.253333-7')}，则只能修改开发版（privacy_ver=2），然后提交代码审核，审核通过之后发布生效。\n' +
		//     '\n' +
		//     '5、当该小程序还没有现网版的隐私保护指引时却传了privacy_ver=1，则会出现 86074 报错'
		// },
		{
			label: t('wxma.wxprivacysetting.253333-0'),
			prop: 'owner_setting.notice_method',
			type: 'input',
			bind: 'owner_setting.notice_method',
			tip: t('wxma.wxprivacysetting.253333-1'),
			render: (form) => {
				if (form?.owner_setting?.notice_method) {
					form.owner_setting = {
						...(form.owner_setting || {}),
						notice_method: '',
					}
				}
				return <el-input v-model={ form.owner_setting.notice_method } />
			},
			rules: [
				{
					required: true,
					message: t('wxma.wxprivacysetting.253333-2'),
					trigger: 'blur',
				},
			],
		},
		{
			label: t('wxma.wxprivacysetting.253333-3'),
			prop: 'owner_setting.contact_email',
			type: 'input',
			tip: t('wxma.wxprivacysetting.253333-4'),
			render: (form) => {
				if (form?.owner_setting?.contact_email) {
					form.owner_setting = {
						...(form.owner_setting || {}),
						contact_email: '',
					}
				}
				return <el-input v-model={ form.owner_setting.contact_email } />
			},
			rules: [
				{
					required: true,
					message: t('wxma.wxprivacysetting.253333-5'),
					trigger: 'blur',
				},
			],
		},
		{
			label: t('wxma.wxprivacysetting.253333-6'),
			prop: 'setting_list',
			colProps: { span: 24 },
			// display: false,
			group: [
				{
					label: t('wxma.wxprivacysetting.253333-7'),
					prop: 'privacy_key',
					type: 'select',
					enums: options.value,
					rules: [
						{
							required: true,
							message: t('wxma.wxprivacysetting.253333-8'),
							trigger: 'blur',
						},
					],
					formslot: true,
				},
				{
					label: t('wxma.wxprivacysetting.253333-9'),
					prop: 'privacy_text',
					type: 'input',
					rules: [
						{
							required: true,
							message: t('wxma.wxprivacysetting.253333-10'),
							trigger: 'blur',
						},
					],
					tip: t('wxma.wxprivacysetting.253333-11'),
				},
			],
		},
	],
};
