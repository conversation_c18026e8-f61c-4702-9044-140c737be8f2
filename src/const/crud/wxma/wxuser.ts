/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.jixiansc.com
 * 注意：
 * 本软件为www.jixiansc.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
	dialogDrag: true,
	border: true,
	index: false,
	indexLabel: t('wxma.wxuser.322133-0'),
	stripe: true,
	menuAlign: 'left',
	align: 'center',
	editBtn: false,
	delBtn: false,
	addBtn: false,
	// excelBtn: true,
	// printBtn: true,
	viewBtn: true,
	searchShow: false,
	menuWidth: 150,
	menuType: 'text',
	searchMenuSpan: 6,
	defaultSort: {
		prop: 'createTime',
		order: 'descending',
	},
	column: [
		{
			label: t('wxma.wxuser.322133-1'),
			prop: 'headimgUrl',
			imgWidth: 50,
			dataType: 'string',
			type: 'images',
			listType: 'picture-img',
			editDisplay: false,
			attrs: {
				viewMode: 'fold',
			},
		},
		{
			label: t('wxma.wxuser.322133-2'),
			prop: 'nickName',
			width: 100,
			// sortable: true,
			search: true,
			editDisplay: false,
		},
		{
			label: 'openId',
			prop: 'openId',
			editDisplay: false,
			search: true,
		},
		{
			label: 'union_id',
			prop: 'unionId',
			editDisplay: false,
			search: true,
		},
		{
			label: t('wxma.wxuser.322133-3'),
			prop: 'sex',
			width: 60,
			type: 'select',
			// sortable: true,
			search: true,
			editDisplay: false,
			slot: true,
			dicUrl: `/admin/dict/type/wx_sex`,
		},
		{
			label: t('wxma.wxuser.322133-4'),
			prop: 'country',
			// sortable: true,
			search: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-5'),
			prop: 'province',
			// sortable: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-6'),
			prop: 'city',
			// sortable: true,
			search: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-7'),
			prop: 'language',
			// sortable: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-8'),
			prop: 'remark',
			hide: true,
		},
		{
			label: t('wxma.wxuser.322133-9'),
			prop: 'createTime',
			// sortable: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-10'),
			prop: 'updateTime',
			// sortable: true,
			hide: true,
			editDisplay: false,
		},
		{
			label: t('wxma.wxuser.322133-11'),
			prop: 'action',
			width: '100',
			fixed: 'right',
		},
	],
};
