/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.jixiansc.com
 * 注意：
 * 本软件为www.jixiansc.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import { getList } from '/@/api/wxma/wxapp';

const validateAppID = (rule, value, callback) => {
	if (window.openType === 'edit') {
		callback();
	} else {
		getList({
			id: value,
		}).then((response) => {
			if (response?.length > 0) {
				callback(new Error(t('wxma.wxapp.144968-0')));
			} else {
				callback();
			}
		});
	}
};

const validateWeixinSign = (rule, value, callback) => {
	if (window.openType === 'edit') {
		callback();
	} else {
		getList({
			weixinSign: value,
		}).then((response) => {
			if (response?.length > 0) {
				callback(new Error(t('wxma.wxapp.144968-1')));
			} else {
				callback();
			}
		});
	}
};

export const tableOption = {
	calcHeight: 80,
	height: 'auto',
	dialogType: 'drawer',
	dialogWidth: '80%',
	border: true,
	stripe: true,
	menuAlign: 'left',
	align: 'center',
	menuWidth: 200,
	menuType: 'text',
	searchShow: true,
	// excelBtn: true,
	// printBtn: true,
	viewBtn: true,
	expand: true,
	defaultExpandAll: true,
	labelWidth: 120,
	searchMenuSpan: 6,
	column: [
		{
			label: t('wxma.wxapp.144968-2'),
			prop: 'organId',
			type: 'cascader',
			search: true,
			hide: true,
		},
		{
			label: t('wxma.wxapp.144968-3'),
			prop: 'name',
			align: 'left',
			search: true,
			component: 'el-input',
			slot: true,
			display: false,
			rules: [
				{
					required: true,
					message: t('wxma.wxapp.144968-4'),
					trigger: 'blur',
				},
			],
		},
		{
			label: t('wxma.wxapp.144968-5'),
			prop: 'weixinSign',
			component: 'el-input',
			search: true,
			hide: true,
			display: false,
		},
		{
			label: 'AppID',
			prop: 'id',
			component: 'el-input',
			search: true,
			hide: true,
			display: false,
		},
		{
			label: t('wxma.wxapp.144968-6'),
			search: false,
			prop: 'action',
			width: 130,
		},
	],
	group: [
		{
			icon: 'el-icon-s-order',
			label: t('wxma.wxapp.144968-7'),
			prop: 'group1',
			column: [
				{
					label: t('wxma.wxapp.144968-8'),
					prop: 'qrCode',
					// type: 'upload',
					span: 24,
					listType: 'picture-img',
					action: `${import.meta.env.VITE_VUE_PROXY_URL}/admin/file/upload?fileType=image&dir=wx/`,
					propsHttp: {
						url: 'link',
					},
					loadText: t('wxma.wxapp.144968-9'),
					tip: t('wxma.wxapp.144968-10'),
				},
				{
					label: t('wxma.wxapp.144968-2'),
					prop: 'organId',
					rules: [
						{
							required: true,
							message: t('wxma.wxapp.144968-11'),
							trigger: 'change',
						},
					],
					type: 'tree',
					search: true,
					props: {
						label: 'name',
						value: 'id',
					},
					defaultExpandAll: true,
					dicUrl: '/admin/dept/tree',
					colProps: { span: 12 },
				},
				{
					label: t('wxma.wxapp.144968-3'),
					prop: 'name',
					align: 'left',
					rules: [
						{
							required: true,
							message: t('wxma.wxapp.144968-4'),
							trigger: 'blur',
						},
					],
					colProps: { span: 12 },
				},
				{
					label: t('wxma.wxapp.144968-5'),
					prop: 'weixinSign',
					editDisabled: true,
					rules: [
						{
							required: true,
							message: t('wxma.wxapp.144968-12'),
							trigger: 'blur',
						},
						{
							validator: validateWeixinSign,
							trigger: 'blur',
						},
					],
					tip: t('wxma.wxapp.144968-13'),
					colProps: { span: 12 },
				},
				{
					label: 'AppID',
					prop: 'id',
					editDisabled: true,
					rules: [
						{
							required: true,
							message: t('wxma.wxapp.144968-14'),
							trigger: 'blur',
						},
						{
							validator: validateAppID,
							trigger: 'blur',
						},
					],
					tip: t('wxma.wxapp.144968-15'),
					colProps: { span: 12 },
				},
				{
					label: 'AppSecret',
					prop: 'secret',
					// type: 'password',
					rules: [
						{
							required: true,
							message: t('wxma.wxapp.144968-16'),
							trigger: 'blur',
						},
					],
					tip: t('wxma.wxapp.144968-17'),
					colProps: { span: 12 },
				},
				{
					label: t('wxma.wxapp.144968-18'),
					prop: 'principalName',
					colProps: { span: 12 },
				},
				{
					label: t('wxma.wxapp.144968-19'),
					prop: 'remarks',
					colProps: { span: 12 },
				},
			],
		},
		// {
		//   icon: 'el-icon-s-order',
		//   label: '协议信息',
		//   prop: 'group2',
		//   labelWidth: 0,
		//   column: [
		//     {
		//       prop: 'agreementInformation',
		//       formslot: true,
		//       span: 24
		//     }
		//   ]
		// }
	],
};

// };
// export const tableOption = {
// 	labelWidth: '120px',
// 	labelPosition: 'right',
// 	size: 'default',
// 	columns: [
// 		{
// 			title: t('wxma.wxapp.144968-3'),
// 			dataIndex: 'name',
// 			align: 'left',
// 			search: true,
// 			slots: { customRender: 'name' },
// 		},
// 		{
// 			title: 'AppID',
// 			dataIndex: 'id',
// 			search: true,
// 		},
// 		{
// 			title: t('wxma.wxapp.144968-18'),
// 			dataIndex: 'principalName',
// 		},
// 		{
// 			title: '创建时间',
// 			dataIndex: 'createTime',
// 			valueType: 'dateTime',
// 		},
// 		{
// 			title: t('wxma.wxapp.144968-6'),
// 			dataIndex: 'action',
// 			slots: { customRender: 'action' },
// 			width: 200,
// 		},
// 	],
// 	formItems: [
// 		{
// 			type: 'upload',
// 			label: t('wxma.wxapp.144968-8'),
// 			prop: 'qrCode',
// 			span: 24,
// 			action: `${import.meta.env.VITE_VUE_PROXY_URL}/admin/file/upload?fileType=image&dir=wx/`,
// 			props: {
// 				url: 'link',
// 			},
// 			tip: t('wxma.wxapp.144968-10'),
// 		},
// 		{
// 			type: 'tree-select',
// 			label: t('wxma.wxapp.144968-2'),
// 			prop: 'organId',
// 			props: {
// 				label: 'name',
// 				value: 'id',
// 			},
// 			dicUrl: '/admin/dept/tree',
// 			rules: [
// 				{
// 					required: true,
// 					message: t('wxma.wxapp.144968-11'),
// 					trigger: 'change',
// 				},
// 			],
// 		},
// 		{
// 			type: 'input',
// 			label: t('wxma.wxapp.144968-3'),
// 			prop: 'name',
// 			rules: [
// 				{
// 					required: true,
// 					message: t('wxma.wxapp.144968-4'),
// 					trigger: 'blur',
// 				},
// 			],
// 		},
// 		{
// 			type: 'input',
// 			label: t('wxma.wxapp.144968-5'),
// 			prop: 'weixinSign',
// 			disabled: true,
// 			rules: [
// 				{
// 					required: true,
// 					message: t('wxma.wxapp.144968-12'),
// 					trigger: 'blur',
// 				},
// 				{
// 					validator: validateWeixinSign,
// 					trigger: 'blur',
// 				},
// 			],
// 			tip: t('wxma.wxapp.144968-13'),
// 		},
// 		{
// 			type: 'input',
// 			label: 'AppID',
// 			prop: 'id',
// 			disabled: true,
// 			rules: [
// 				{
// 					required: true,
// 					message: t('wxma.wxapp.144968-14'),
// 					trigger: 'blur',
// 				},
// 				{
// 					validator: validateAppID,
// 					trigger: 'blur',
// 				},
// 			],
// 			tip: t('wxma.wxapp.144968-15'),
// 		},
// 		{
// 			type: 'password',
// 			label: 'AppSecret',
// 			prop: 'secret',
// 			rules: [
// 				{
// 					required: true,
// 					message: t('wxma.wxapp.144968-16'),
// 					trigger: 'blur',
// 				},
// 			],
// 			tip: t('wxma.wxapp.144968-17'),
// 		},
// 		{
// 			type: 'input',
// 			label: t('wxma.wxapp.144968-18'),
// 			prop: 'principalName',
// 		},
// 		{
// 			type: 'input',
// 			label: t('wxma.wxapp.144968-19'),
// 			prop: 'remarks',
// 		},
// 	],
// };
