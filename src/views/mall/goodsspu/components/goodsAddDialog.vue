<template>
	<yun-drawer
		v-model="visible"
		:title="mode == 'edit' ? $t('components.goodsAddDialog.318920-0') : $t('components.goodsAddDialog.318920-1')"
		destroy-on-close
		append-to-body
		size="85%"
	>
		<yun-pro-form
			ref="formRef"
			:form="formData"
			:columns="formColumns"
			:form-props="{ labelPosition: 'top' }"
			:config="{ grid: true, colProps: { span: 12 }, rowProps: { gutter: 16 } }"
		>
		</yun-pro-form>

		<template #footer>
			<span class="flex justify-end">
				<el-button @click="handleCancel">{{ $t('components.goodsAddDialog.318920-2') }}</el-button>
				<el-button type="primary" @click="handleSave">{{ $t('components.goodsAddDialog.318920-3') }}</el-button>
			</span>
		</template>
	</yun-drawer>
</template>

<script setup lang="tsx">
import { computed, ref, onMounted, onBeforeMount, nextTick, watch } from 'vue';
import { ElMessage } from 'yun-design';
import { useFormOptions } from '../hooks/useOptions.jsx';
import { fetchTree } from '@/api/mall/goodscategory';
import { getSupplierGoodsPage, getSpecList, getDepositList, getObj, addObj, putObj } from '@/api/mall/goodsspu';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { deepClone } from '@/utils/util';
import PinYin from 'js-pinyin';
PinYin.setOptions({ checkPolyphone: false, charCase: 1 });

const stores = useUserInfo();
const { shopInfo } = storeToRefs(stores);
const shopId = shopInfo.value.id;

const props = defineProps({
	activeCode: {
		type: String,
		default: '',
	},
	data: {
		type: Object,
		default: () => {},
	},
	id: {
		type: String,
		default: '',
	},
	mode: {
		type: String,
		default: 'add',
	},
	row: {
		type: Object,
		default: {},
	},
});

const emit = defineEmits(['confirm', 'onSuccess']);
const visible = ref(false);
const formData = ref({
	purchaseType: 'SUPPLIER_DELIVERY',
	description: '<p><span style="color: rgb(230, 0, 0);">sadasdasd</span></p>',
	picUrls: [],
});
const formRef = ref(null);
const unitOptions = ref([]);
const depositList = ref([]);
const specTypeTableData = ref([]);
const editSkuStock = ref([]);
const serverMode = ref('SINGLE');
const reFreshMaterialList = ref(false);

// onBeforeMount(async () => {
// 	const res = await getShopMode();
// 	serverMode.value = res.data?.serverMode;
// });

onMounted(() => {
	fetchCategoryOptions({});
	getSpecList({}).then((res) => {
		unitOptions.value = (res?.data || []).map((item) => {
			const { name: label, id: value } = item;
			return {
				...item,
				label,
				value,
			};
		});
	});
});

const goodsLevelList = ref([]);
const supplyList = ref([]);
const fetchCategoryOptions = async (params) => {
	const { data } = await fetchTree(params);
	goodsLevelList.value = data ?? [];
};

getSupplierGoodsPage({ shopId }).then((res) => {
	supplyList.value = res?.data ?? [];
});

//通过店铺联动押金品
// getDepositList(shopId).then((res) => {
// 	//循环res?.data?.data 将depositName和price 加上单位￥ 拼接成一个字符串
// 	(res?.data ?? []).forEach((item) => {
// 		item.depositName = `${item.depositName} ￥${item.price}`;
// 	});
// 	depositList.value = res?.data ?? [];
// });

const confirmHandler = () => {
	return new Promise((resolve, reject) => {
		formRef.value.elForm?.validate?.(async (valid) => {
			if (valid) {
				emit('confirm');
			} else {
				reject();
			}
		});
	});
};

const open = () => {
	visible.value = true;
	resetFrom();
	setTimeout(() => {
		loadData();
	});
};

const unitTypeList = computed(() => {
	const result = unitOptions.value.filter((item) => specTypeTableData.value.some((inner) => inner.currentUnitId === item.id));
	if (result.length === 1) {
		formData.value.saleUnitName = result[0].name; // eslint-disable-line
	}
	return result;
});

const currentUnitName = computed(() => {
	const first = specTypeTableData.value[0];
	const { currentUnitId } = first || {};
	const item = unitOptions.value.find((v) => v.id === currentUnitId);
	return item?.name || '';
});

//传入的id为一个数组，有两个值，第一个值为当前选中的一级分类，第二个值为当前选中的二级分类 获取this.unitOptions中第第二级的name
const changeUnit = (val, index) => {
	if (index) {
		const item = specTypeTableData.value[index];
		const { currentUnitId } = item || {};
		const option = unitOptions.value.find((v) => v.id === currentUnitId);
		item.specContent = `${item.convertRatio || ''}${currentUnitName.value}/${option.name}`;
	} else {
		specTypeTableData.value = specTypeTableData.value.map((item, index) =>
			index !== 0 ? { ...item, currentUnitId: null, specContent: null } : { ...item, specContent: null }
		);
	}
};

//监听是否可售卖值的变化
const isSaleChange = (val, index) => {
	if (specTypeTableData.value.length === 1 && val === 'NO') {
		specTypeTableData.value[0].whetherPurchaseUnit = 'YES';
		// setTimeout(() => {
		// 	ElMessage.warning('采购单位为必选');
		// });
		return;
	}
	specTypeTableData.value.forEach((item, i) => i !== index && (item.whetherPurchaseUnit = 'NO'));
};

//规格新增行
const handleAddRow = (index, row) => {
	let addRow = {
		enable: '1',
		whetherPurchaseUnit: 'NO',
	};

	// 如果是非标品且称重，新增行的基础单位默认为kg
	if (formData.value.whetherStandard === 'NO' && formData.value.whetherWeight === 'YES') {
		const kgUnit = unitOptions.value.find(unit => unit.name === 'kg' || unit.label === 'kg');
		if (kgUnit && index === 0) {
			addRow.currentUnitId = kgUnit.value;
		}
	}

	specTypeTableData.value.splice(index + 1, 0, addRow);
};
//规格删除行
const handleDeleteRow = (index, row) => {
	specTypeTableData.value.splice(index, 1);
	const item = specTypeTableData.value.find((v) => v.whetherPurchaseUnit === 'YES');
	if (!item) {
		specTypeTableData.value[0].whetherPurchaseUnit = 'YES';
	}
};

const changeRowData = (item) => {
	const row = deepClone(item);
	row.baseUnitName = currentUnitName.value;
	// row.syncSystem = item.syncSystem.join(',')
	for (let i = 0; i < row.skus.length; i++) {
		//补充row.skus中的最小单位名称字段
		const sku = row.skus[i];
		sku.baseUnitName = currentUnitName.value;
		//补充row.skus中的是否为最小单位字段
		if (i === 0) {
			sku.whetherBaseUnit = 'YES';
			sku.unitPrice = sku.salesPrice;
			sku.convertRatio = 1;
			sku.currentUnitName = currentUnitName.value;
		} else {
			sku.whetherBaseUnit = 'NO';
			//计算最小单位价格保留两位小数
			sku.unitPrice = parseFloat(sku.salesPrice / sku.convertRatio).toFixed(2);
			unitOptions.value.forEach((item) => {
				if (item.id === sku.currentUnitId) {
					sku.currentUnitName = item.name;
				}
			});
		}
		sku.currentUnitId = [sku.currentUnitId];
	}
	if (row.defaultSupplierId) {
		supplyList.value.forEach((item) => {
			if (item.id === row.defaultSupplierId) {
				row.defaultSupplierName = item.name;
			}
		});
	}
	row.whetherStandard === 'YES' && (row.whetherWeight = 'NO');
	row.inputTax = row.inputTax || 0;
	row.outputTax = row.outputTax || 0;
	row.categoryFirst = row.categoryId[0] || null;
	row.categorySecond = row.categoryId[1] || null;
	// row.categoryShopFirst = row?.categoryShopId[0] || null;
	// row.categoryShopSecond = row?.categoryShopId[1] || null;

	// 默认不称重，默认市场自采，默认虚拟库存
	if (serverMode.value === 'MALL') {
		row.whetherWeight = 'NO';
		row.purchaseType = 'CUSTOM_PURCHASE';
		row.saleType = 'VIRTUAL_STOCK';
	}
	return row;
};

/**
 * @title 数据添加
 * @param row 为当前的数据
 * @param done 为表单关闭函数
 *
 **/
const handleSave = () => {
	formRef.value.elForm?.validate?.(async (valid) => {
		if (valid) {
			if (!formData.value.easyCode) {
				formData.value.easyCode = PinYin.getFullChars(formData.value.name);
			}
			formData.value.skus = specTypeTableData.value;
			let validOk = true;
			formData.value.skus.forEach((val, index) => {
				if (!val.currentUnitId) {
					validOk = false;
					ElMessage({
						showClose: true,
						message: t('components.goodsAddDialog.318920-4'),
						type: 'error',
					});
					return;
				}
				if (index > 0 && !val.convertRatio) {
					validOk = false;
					ElMessage({
						showClose: true,
						message: t('components.goodsAddDialog.318920-5'),
						type: 'error',
					});
					return;
				}

				if (!val.purchasePrice) {
					validOk = false;
					ElMessage({
						showClose: true,
						message: t('components.goodsAddDialog.408177-0'),
						type: 'error',
					});
					return;
				}

				if (val.salesPrice && val.salesPrice < val.purchasePrice) {
					validOk = false;
					ElMessage({
						showClose: true,
						message: t('components.goodsAddDialog.408177-1'),
						type: 'error',
					});
					return;
				}

				if (!val.salesPrice) {
					validOk = false;
					ElMessage({
						showClose: true,
						message: t('components.goodsAddDialog.318920-6'),
						type: 'error',
					});
					return;
				}
			});
			if (validOk) {
				const row = changeRowData(formData.value);
				const Service = props.mode === 'add' ? addObj : putObj;
				await Service({ ...row, shopId, qualificationAttachment: [] });
				ElMessage({
					showClose: true,
					message: props.mode === 'add' ? t('components.goodsAddDialog.318920-7') : t('components.goodsAddDialog.318920-8'),
					type: 'success',
				});
				resetFrom();
				handleCancel();
				emit('onSuccess');
			} else {
				return false;
			}
		}
	});
};

const handleCancel = () => {
	visible.value = false;
	resetFrom();
};

const handlePurchaseChange = (val) => val !== 'SUPPLIER_DELIVERY' && (formData.value.defaultSupplierId = '');

const handleSaleTypeChange = (val) => {
	switch (val) {
		case 'REAL_STOCK':
			specTypeTableData.value.forEach((item, index) =>
				props.mode === 'edit' ? (specTypeTableData.value[index].stock = editSkuStock.value[index]) : (item.stock = '')
			);
			break;
		default:
			specTypeTableData.value.forEach((item) => (item.stock = ''));
	}
};

// 处理是否标品和是否称重字段变化的逻辑
const handleStandardOrWeightChange = () => {
	nextTick(() => {
		if (specTypeTableData.value.length > 0) {
			// 如果是非标品且称重，设置基础单位为kg且不可编辑
			if (formData.value.whetherStandard === 'NO' && formData.value.whetherWeight === 'YES') {
				const kgUnit = unitOptions.value.find(unit => unit.name === 'kg' || unit.label === 'kg');
				if (kgUnit) {
					specTypeTableData.value[0].currentUnitId = kgUnit.value;
					// 更新基础单位名称
					changeUnit(kgUnit.value, 0);
				}
			} else {
				// 从非标品且称重状态切换到其他状态时，将基础单位置空
				specTypeTableData.value[0].currentUnitId = null;
				// 清空基础单位名称
				changeUnit(null, 0);
			}
		}
	});
};

// 监听表单数据变化
watch(
	() => [formData.value.whetherStandard, formData.value.whetherWeight],
	() => {
		handleStandardOrWeightChange();
	},
	{ deep: true }
);

const fetchDetail = async () => {
	const response = await getObj(props.row.id);
	editSkuStock.value = response.data.skus.map((item) => item.stock);
	formData.value = { ...(response.data ?? {}) };
	let tableArr = formData.value.skus || [];
	if (!tableArr || tableArr.length == 0) {
		tableArr = [
			{
				enable: '1',
				whetherPurchaseUnit: 'YES',
			},
		];
	}
	//遍历tableArr,将其中whetherBaseUnit为YES的项放到第一项
	tableArr.forEach((item, index) => {
		item.currentUnitId = (item.currentUnitId || '').toString();
		if (item.whetherBaseUnit === 'YES') {
			// currentUnitName.value = item.baseUnitName;
			tableArr.splice(index, 1);
			tableArr.unshift(item);
		}
	});
	specTypeTableData.value = tableArr;
	let categoryIdArr = [];
	if (response.data.categoryFirst) {
		categoryIdArr.push(response.data.categoryFirst);
	}
	if (response.data.categorySecond) {
		categoryIdArr.push(response.data.categorySecond);
	}
	// let categoryShopIdArr = [];
	// if (response.data.categoryShopFirst) {
	// 	categoryShopIdArr.push(response.data.categoryShopFirst);
	// }
	// if (response.data.categoryShopSecond) {
	// 	categoryShopIdArr.push(response.data.categoryShopSecond);
	// }
	formData.value.categoryId = categoryIdArr;
	// formData.value.categoryShopId = categoryShopIdArr;
	formData.value.qualityDay = response.data.qualityDay ? `${response.data.qualityDay}` : '';
	formData.value.description = response.data.description;
	formData.value.syncSystem = response.data?.syncSystem;
};

const { formColumns } = useFormOptions({
	goodsLevelList,
	supplyList,
	unitTypeList,
	serverMode,
	unitOptions,
	depositList,
	changeUnit,
	currentUnitName,
	isSaleChange,
	specTypeTableData,
	handleAddRow,
	handleDeleteRow,
	handlePurchaseChange,
	handleSaleTypeChange,
	handleStandardOrWeightChange,
	reFreshMaterialList,
	formData,
	mode: props.mode,
});

const loadData = () => {
	unitOptions.value.forEach((item) => (item.disabled = false));
	reFreshMaterialList.value = false;
	nextTick(() => {
		reFreshMaterialList.value = true;
	});
	if (props.mode === 'add') {
		formData.value.defaultSupplierId = '';
		formData.value.shopId = shopId;
		specTypeTableData.value = [
			{
			enable: '1',
			whetherPurchaseUnit: 'YES',
			},
		];
		currentUnitName.value = '';
		serverMode.value === 'MALL' && (formData.value.saleType = 'VIRTUAL_STOCK');
	} else {
		fetchDetail();
	}
};

const resetFrom = async () => {
	formData.value = {};
	formData.value.purchaseType = 'SUPPLIER_DELIVERY';
	formData.value.shelf = '0';
	formData.value.picUrls = [];
	specTypeTableData.value = [];

	if (formRef.value) {
		await formRef.value.elForm?.resetFields();
		await formRef.value.elForm.clearValidate();
	}
};

defineExpose({
	confirmHandler,
	open,
	close,
});
</script>

<style lang="scss" scoped></style>
