import { ref, computed, onMounted } from 'vue';
import { fetchTree } from '@/api/mall/goodscategory';
import BaseEditor from '@/components/Editor/BaseEditor.vue';
import MaterialList from '@/components/Material/list.vue';
import { TaxRateOptions } from '@/views/mall/purchaseorder/const';

const goodsLevelList = ref([]);
const fetchCategoryOptions = async (params) => {
	const { data } = await fetchTree(params);
	goodsLevelList.value = data ?? [];
};

const positiveIntegerReg = /^[1-9]\d*$/;
const validatePositiveIntegerReg = (rule, value, callback) => {
	if (value === 0) {
		callback(new Error(t('hooks.useOptions.889925-0')));
	}
	if (value && value !== '' && !positiveIntegerReg.test(value)) {
		callback(new Error(t('hooks.useOptions.889925-0')));
	}
	if (value > 9999999) {
		callback(new Error(t('hooks.useOptions.889925-1')));
	}
	callback();
};

const validateQualityDay = (rule, value, callback) => {
	if (value === 0) {
		callback(new Error(t('hooks.useOptions.889925-0')));
	}
	if (value && value !== '' && !positiveIntegerReg.test(value)) {
		callback(new Error(t('hooks.useOptions.889925-0')));
	}
	if (value > 99999) {
		callback(new Error(t('hooks.useOptions.889925-2')));
	}
	callback();
};

const SaleTypeOptions = [
	{
		label: t('hooks.useOptions.889925-3'),
		value: 'REAL_STOCK',
	},
	{
		label: t('hooks.useOptions.889925-4'),
		value: 'LIMIT_STOCK',
	},
	// {
	// 	label: t('hooks.useOptions.889925-64'),
	// 	value: 'VIRTUAL_STOCK',
	// },
];

const PurchaseTypeOptions = [
	{
		label: t('hooks.useOptions.889925-5'),
		value: 'SUPPLIER_DELIVERY',
	},
	// {
	// 	label: '市场自采',
	// 	value: 'CUSTOM_PURCHASE',
	// },
];

export default () => {
	const tableColumns = [
		{
			type: 'selection',
			width: 52,
			fixed: 'left',
		},
		{
			label: t('hooks.useOptions.889925-6'),
			prop: 'spuCode',
		},
		{
			label: t('hooks.useOptions.889925-7'),
			prop: 'picUrls',
			'show-overflow-tooltip': false,
		},
		{
			label: t('hooks.useOptions.889925-8'),
			prop: 'name',
			width: 160,
		},
		{
			label: t('hooks.useOptions.889925-9'),
			prop: 'baseUnitName',
		},
		{
			label: t('hooks.useOptions.889925-10'),
			prop: 'saleType',
			enums: SaleTypeOptions,
		},
		// {
		// 	label: '采购类型',
		// 	prop: 'purchaseType',
		// 	enums: PurchaseTypeOptions,
		// },
		{
			label: t('hooks.useOptions.889925-11'),
			prop: 'defaultSupplierName',
			width: 120,
		},
		{
			label: t('hooks.useOptions.889925-12'),
			prop: 'priceDown',
			width: 160,
			render({ row }) {
				return (
					<div style="color: red">
						￥{row.priceDown}
						{row.priceUp == row.priceDown ? '' : '~￥' + row.priceUp}
					</div>
				);
			},
		},
		{
			label: t('hooks.useOptions.889925-13'),
			prop: 'availableStock',
			render({ row }) {
				const str = `${t('hooks.useOptions.889925-14')}${row.availableStock}
        ${
					row.saleType === 'REAL_STOCK'
						? `${t('hooks.useOptions.889925-15')}${row.totalStock || 0}${t('hooks.useOptions.889925-16')}${row.occupyStock || 0}`
						: ''
				}
        `;

				return (
					<el-tooltip content={str} placement="top">
						<div>{row.availableStock}</div>
					</el-tooltip>
				);
			},
		},
		{
			label: t('hooks.useOptions.889925-17'),
			prop: 'shelf',
			enums: [
				{
					label: t('hooks.useOptions.889925-18'),
					value: '0',
				},
				{
					label: t('hooks.useOptions.889925-19'),
					value: '1',
				},
			],
		},
		{
			label: t('hooks.useOptions.889925-20'),
			prop: 'verifyStatus',
			enums: [
				{
					label: t('hooks.useOptions.889925-21'),
					value: '0',
				},
				{
					label: t('hooks.useOptions.889925-22'),
					value: '1',
				},
				{
					label: t('hooks.useOptions.889925-23'),
					value: '2',
				},
			],
			render({ row }) {
				return (
					<div>
						{row.verifyStatus == 0 && (
							<el-tag v-if={row.verifyStatus == 0} size="small">
								{t('hooks.useOptions.889925-21')}
							</el-tag>
						)}
						{row.verifyStatus == 1 && (
							<el-tag v-if={row.verifyStatus == 1} size="small" type="success">
								{t('hooks.useOptions.889925-22')}
							</el-tag>
						)}

						{row.verifyStatus == 2 && (
							<el-tag v-if={row.verifyStatus == 2} size="small" type="danger">
								{t('hooks.useOptions.889925-23')}
							</el-tag>
						)}
					</div>
				);
			},
		},
		{
			label: t('hooks.useOptions.889925-24'),
			prop: 'action',
		},
	];
	const searchFields = computed(() => [
		{
			label: t('hooks.useOptions.889925-8'),
			prop: 'name',
			component: 'el-input',
			componentAttrs: {
				placeholder: t('hooks.useOptions.889925-25'),
			},
		},
		{
			label: t('hooks.useOptions.889925-26'),
			prop: 'categoryId',
			component: 'el-cascader',
			componentAttrs: {
				clearable: true,
				props: {
					label: 'name',
					value: 'id',
					children: 'children',
				},
				options: goodsLevelList.value.filter((v) => v.children && v.children.length),
			},
		},
		{
			label: t('hooks.useOptions.889925-27'),
			prop: 'easyCode',
			component: 'el-input',
			componentAttrs: {
				placeholder: t('hooks.useOptions.889925-28'),
			},
			tip: t('hooks.useOptions.889925-29'),
		},
		// {
		// 	label: '采购类型',
		// 	prop: 'purchaseType',
		// 	component: 'el-select',
		// 	componentAttrs: {
		// 		clearable: true,
		// 		props: {
		// 			label: 'label',
		// 			value: 'value',
		// 		},
		// 		onChange: () => {},
		// 	},
		// 	options: PurchaseTypeOptions,
		// },
		{
			label: t('hooks.useOptions.889925-10'),
			prop: 'saleType',
			component: 'el-select',
			componentAttrs: {
				clearable: true,
				props: {
					label: 'label',
					value: 'value',
				},
			},
			options: SaleTypeOptions,
		},
		{
			label: t('hooks.useOptions.889925-11'),
			prop: 'defaultSupplierName',
			component: 'el-input',
			componentAttrs: {
				placeholder: t('hooks.useOptions.889925-30'),
			},
		},
		{
			label: t('hooks.useOptions.889925-31'),
			prop: 'shelf',
			component: 'el-select',
			componentAttrs: {
				clearable: true,
				props: {
					label: 'label',
					value: 'value',
				},
			},
			options: [
				{
					label: t('hooks.useOptions.889925-18'),
					value: '0',
				},
				{
					label: t('hooks.useOptions.889925-19'),
					value: '1',
				},
			],
		},
	]);
	onMounted(() => {
		fetchCategoryOptions();
	});
	return { tableColumns, searchFields };
};

export const useFormOptions = ({
	goodsLevelList,
	supplyList,
	unitTypeList,
	serverMode,
	unitOptions,
	depositList,
	changeUnit,
	currentUnitName,
	isSaleChange,
	specTypeTableData,
	handleAddRow,
	handleDeleteRow,
	handlePurchaseChange,
	handleSaleTypeChange,
	handleStandardOrWeightChange,
	reFreshMaterialList,
	formData,
	mode,
}) => {
	const editTableColumns = [
		{
			label: t('hooks.useOptions.889925-9'),
			headerRender() {
				return <div class="wk-required">{t('hooks.useOptions.889925-9')}</div>;
			},
			required: true,
			prop: 'currentUnitId',
			width: 320,
			'show-overflow-tooltip': false,
			render: (data) => {
				const { row, $index: index } = data;
				const { salesPrice, convertRatio } = row;
				return (
					<>
						{index === 0 && (
							<div>
								{t('hooks.useOptions.625688-0')}
								<el-select style="width: 100px !important" filterable v-model={row.currentUnitId} onChange={(val) => changeUnit(val)}
									disabled={
										mode === 'edit' ||
										(formData.value.whetherStandard === 'NO' && formData.value.whetherWeight === 'YES')
									}
								>
									{(unitOptions.value ?? []).map((option) => {
										// 如果是非标品且称重，只显示kg选项
										if (formData.value.whetherStandard === 'NO' && formData.value.whetherWeight === 'YES') {
											if (option.name === 'kg' || option.label === 'kg') {
												return <el-option label={option.label} value={option.value} key={option.value}></el-option>;
											}
											return null;
										}
										return <el-option label={option.label} value={option.value} key={option.value}></el-option>;
									})}
								</el-select>
							</div>
						)}
						{index !== 0 && (
							<div style="display: flex;align-items: center">
								<el-select style="width: 100px !important" filterable v-model={row.currentUnitId} onChange={(val) => changeUnit(val, index)}>
									{(unitOptions.value ?? []).map((option) => {
										const first = specTypeTableData.value[0];
										const { currentUnitId } = first || {};
										const disabled = currentUnitId === option.value;
										return <el-option label={option.label} value={option.value} disabled={disabled} key={option.value}></el-option>;
									})}
								</el-select>
								=
								<el-input-number
									style="width: 100px !important"
									v-model={row.convertRatio}
									placeholder={t('hooks.useOptions.889925-33')}
									controls={false}
									onChange={() => {
										changeUnit(row.currentUnitId, index);
									}}
									min={0}
									label={t('hooks.useOptions.889925-33')}
								></el-input-number>
								{salesPrice && convertRatio ? (
									<div>
										{(salesPrice / convertRatio).toFixed(2)}
										{t('hooks.useOptions.889925-34')}
										{currentUnitName.value}
									</div>
								) : (
									<div>{currentUnitName.value}</div>
								)}
							</div>
						)}
					</>
				);
			},
		},
		{
			label: t('hooks.useOptions.889925-35'),
			prop: 'specContent',
			'show-overflow-tooltip': false,
			render: ({ row, $index: index }) => {
				return <el-input v-model={row.specContent} placeholder={t('hooks.useOptions.889925-36')}></el-input>;
			},
		},
		{
			label: '单位条形码',
			prop: 'barCode',
			'show-overflow-tooltip': false,
			render: ({ row, $index: index }) => {
				return <el-input v-model={row.barCode} placeholder={'请输入单位条形码'}></el-input>;
			},
		},
		// {
		// 	label: '押金品',
		// 	prop: 'depositId',
		// 	render: ({ row, $index: index }) => (
		// 		<el-select clearable v-model={row.depositId} placeholder="{t('hooks.useOptions.889925-58')}押金品" style="width: 160px !important;margin-left: 10px">
		// 			{(depositList.value ?? []).map((item) => (
		// 				<el-option key={item.depositCode} label={item.depositName} disabled={item.enableStatus !== 'ENABLE'} value={item.depositCode}></el-option>
		// 			))}
		// 		</el-select>
		// 	),
		// },
		{
			label: t('hooks.useOptions.889925-17'),
			prop: 'enable',
			render: ({ row, $index: index }) => {
				return <el-switch v-model={row.enable} activeValue="1" inactiveValue="0" active-color="#13ce66" inactive-color="#909399"></el-switch>;
			},
		},
		{
			label: t('hooks.useOptions.889925-38'),
			prop: 'whetherPurchaseUnit',
			render: ({ row, $index: index }) => {
				return (
					<el-switch
						v-model={row.whetherPurchaseUnit}
						activeValue="YES"
						inactiveValue="NO"
						active-color="#13ce66"
						inactive-color="#909399"
						onChange={(val) => {
							isSaleChange(val, index);
						}}
					></el-switch>
				);
			},
		},
		{
			label: '默认采购价',
			prop: 'purchasePrice',
			'show-overflow-tooltip': false,
			render: ({ row, $index: index }) => {
				return <el-input-number v-model={row.purchasePrice} controls={false} min={0} placeholder={'请输入默认采购价'}></el-input-number>;
			},
		},
		{
			label: '默认销售价格',
			prop: 'salesPrice',
			'show-overflow-tooltip': false,
			render: ({ row, $index: index }) => {
				return <el-input-number v-model={row.salesPrice} controls={false} min={0} placeholder={'请输入默认销售价格'}></el-input-number>;
			},
		},
		{
			label: t('hooks.useOptions.889925-39'),
			prop: 'specDesc',
			render: ({ row, $index: index }) => <el-input placeholder={t('hooks.useOptions.889925-40')} v-model={row.specDesc}></el-input>,
		},
		{
			label: t('hooks.useOptions.889925-24'),
			prop: 'action',
			width: 100,
			fixed: 'left',
			render: ({ row, $index }) => (
				<>
					<el-button type="text" size="mini" onClick={() => handleAddRow($index, row)}>
						{t('hooks.useOptions.889925-41')}
					</el-button>
					{$index !== 0 && (
						<el-button type="text" size="mini" type="danger" onClick={() => handleDeleteRow($index, row)}>
							{t('hooks.useOptions.889925-42')}
						</el-button>
					)}
				</>
			),
		},
	];

	const formColumns = computed(() => [
		{
			label: t('hooks.useOptions.889925-43'),
			colProps: { span: 24 },
			groups: [
				{
					prop: 'name',
					label: t('hooks.useOptions.889925-8'),
					type: 'input',
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-44'),
							trigger: 'blur',
						},
						{
							max: 30,
							message: t('hooks.useOptions.889925-45'),
						},
					],
				},
				{
					prop: 'categoryId',
					label: t('hooks.useOptions.889925-26'),
					type: 'cascader',
					attrs: {
						clearable: true,
						props: {
							label: 'name',
							value: 'id',
							children: 'children',
						},
						options: goodsLevelList.value,
					},
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-46'),
							trigger: 'blur',
						},
					],
				},
				{
					prop: 'whetherStandard',
					label: t('hooks.useOptions.889925-47'),
					type: 'radio',
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-48'),
							trigger: 'blur',
						},
					],
					attrs: {
						onChange: handleStandardOrWeightChange,
					},
					enums: [
						{
							label: t('hooks.useOptions.889925-49'),
							value: 'YES',
						},
						{
							label: t('hooks.useOptions.889925-50'),
							value: 'NO',
						},
					],
				},
				{
					show: (form) => serverMode.value !== 'MALL' && form.whetherStandard === 'NO',
					prop: 'whetherWeight',
					label: t('hooks.useOptions.889925-51'),
					type: 'radio',
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-52'),
							trigger: 'blur',
						},
					],
					attrs: {
						onChange: handleStandardOrWeightChange,
					},
					enums: [
						{
							label: t('hooks.useOptions.889925-53'),
							value: 'YES',
						},
						{
							label: t('hooks.useOptions.889925-54'),
							value: 'NO',
						},
					],
				},
				// {
				// 	show: () => serverMode.value !== 'MALL',
				// 	prop: 'purchaseType',
				// 	label: '采购类型',
				// 	type: 'radio',
				// 	attrs: {
				// 		clearable: true,
				// 		props: {
				// 			label: 'label',
				// 			value: 'value',
				// 		},
				// 		onChange: handlePurchaseChange,
				// 	},
				// 	enums: PurchaseTypeOptions,
				// },
				{
					// show: (form) => form.purchaseType === 'SUPPLIER_DELIVERY',
					prop: 'defaultSupplierId',
					label: t('hooks.useOptions.889925-11'),
					type: 'select',
					enums: supplyList.value.map((item) => ({ label: item.name, value: item.id })),
					attrs: {
						clearable: true,
						filterable: true,
					},
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-55'),
							trigger: 'blur',
						},
					],
				},
				{
					label: t('hooks.useOptions.889925-27'),
					prop: 'easyCode',
					tip: t('hooks.useOptions.889925-29'),
					type: 'input',
				},
				{
					label: t('hooks.useOptions.889925-56'),
					prop: 'longTerm',
					type: 'radio',
					tip: t('hooks.useOptions.889925-57'),
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-58'),
							trigger: 'blur',
						},
					],
					attrs: {
						// 当选择{t('hooks.useOptions.889925-56')}为{t('hooks.useOptions.889925-60')}时，清空{t('hooks.useOptions.889925-61')}
						onChange: () => {
							formData.value.qualityDay = null;
						},
					},
					enums: [
						{
							label: t('hooks.useOptions.889925-59'),
							value: '0',
						},
						{
							label: t('hooks.useOptions.889925-60'),
							value: '1',
						},
					],
				},
				{
					label: t('hooks.useOptions.889925-61'),
					prop: 'qualityDay',
					type: 'input',
					show: (form) => String(form.longTerm) === '0',
					attrs: {
						maxlength: 5,
						placeholder: t('hooks.useOptions.889925-62'),
					},
					rules: [
						{ required: true, max: 99999, message: t('hooks.useOptions.889925-62'), trigger: 'blur' },
						{
							validator: validateQualityDay,
						},
					],
				},
				{
					label: t('hooks.useOptions.889925-10'),
					prop: 'saleType',
					type: 'radio',
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-63'),
							trigger: 'blur',
						},
					],
					attrs: {
						clearable: true,
						onChange: handleSaleTypeChange,
					},
					enums:
						serverMode.value !== 'MALL'
							? SaleTypeOptions
							: [
									{
										label: t('hooks.useOptions.889925-64'),
										value: 'VIRTUAL_STOCK',
									},
							  ],
					tip: t('hooks.useOptions.889925-65'),
				},
				{
					show: (form) => form.saleType === 'LIMIT_STOCK',
					label: t('hooks.useOptions.889925-66'),
					prop: 'saleStock',
					attrs: {
						maxlength: 7,
					},
					rules: [
						{ max: 9999999, required: true, message: t('hooks.useOptions.889925-67'), trigger: 'blur' },
						{
							validator: validatePositiveIntegerReg,
						},
					],
					render(form) {
						return (
							<div class="flex">
								<div>
									<el-input placeholder={t('hooks.useOptions.889925-67')} v-model={form.saleStock} style="width: 200px" type="number" />
								</div>
								<el-select v-model={form.saleUnitName} style="width: 32% !important; margin-left: 5px" placeholder={t('hooks.useOptions.889925-68')}>
									{unitTypeList.value.map((item, index) => (
										<el-option key={item.id} label={item.name} value={item.name}></el-option>
									))}
								</el-select>
								{form.id && (
									<span style="display: flex; align-items: center; margin-left: 6px;">
										{t('hooks.useOptions.889925-69')}
										{form.availableStock}
										{form.saleType === 'REAL_STOCK' ? `${t('hooks.useOptions.889925-70')}` + (form.occupyStock ? form.occupyStock : 0) : ''}
									</span>
								)}
							</div>
						);
					},
				},
				{
					label: t('hooks.useOptions.889925-71'),
					prop: 'inputTax',
					type: 'select',
					attrs: {
						clearable: true,
					},
					enums: TaxRateOptions,
					// rules: [
					//   {
					//     required: true,
					//     message: '{t('hooks.useOptions.889925-71')}必填',
					//     trigger: 'blur'
					//   },
					// ]
				},
				{
					label: t('hooks.useOptions.889925-72'),
					prop: 'outputTax',
					type: 'select',
					attrs: {
						clearable: true,
					},
					enums: TaxRateOptions,
					// rules: [
					//   {
					//     required: true,
					//     message: '{t('hooks.useOptions.889925-72')}必填',
					//     trigger: 'blur'
					//   },
					// ]
				},
				// {
				// 	show: (form) => serverMode.value !== 'MALL',
				// 	label: '标签',
				// 	prop: 'deliveryTag',
				// 	type: 'checkbox',
				// 	enums: [
				// 		{
				// 			label: '早配',
				// 			value: 'MORNING_DELIVERY',
				// 		},
				// 		{
				// 			label: '明天下午送',
				// 			value: 'TOMORROW_AFTERNOON',
				// 		},
				// 	],
				// },
				{
					label: t('hooks.useOptions.889925-17'),
					prop: 'shelf',
					type: 'radio',
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-73'),
							trigger: 'blur',
						},
					],
					enums: [
						{
							label: t('hooks.useOptions.889925-18'),
							value: '0',
						},
						{
							label: t('hooks.useOptions.889925-19'),
							value: '1',
						},
					],
				},
			],
		},
		{
			label: t('hooks.useOptions.889925-74'),
			colProps: { span: 24 },
			groups: [
				{
					pure: true,
					colProps: { span: 24 },
					render() {
						return <yun-pro-table tableData={specTypeTableData.value} table-columns={editTableColumns}></yun-pro-table>;
					},
				},
			],
		},
		{
			label: t('hooks.useOptions.889925-75'),
			colProps: { span: 24 },
			groups: [
				{
					prop: 'picUrls',
					label: t('hooks.useOptions.889925-7'),
					colProps: { span: 24 },
					render: (form) => {
						return (
							<>
								{reFreshMaterialList && (
									<>
										<MaterialList v-model={form.picUrls} type="image" /> <p style="margin-top: 12px">{t('hooks.useOptions.889925-76')}</p>
									</>
								)}
							</>
						);
					},
					rules: [
						{
							required: true,
							message: t('hooks.useOptions.889925-77'),
							trigger: 'blur',
						},
					],
				},
				{
					prop: 'description',
					label: t('hooks.useOptions.889925-39'),
					render: (form) => <BaseEditor v-model={form.description} />,
					colProps: { span: 24 },
				},
			],
		},
	]);
	return {
		formColumns,
	};
};
