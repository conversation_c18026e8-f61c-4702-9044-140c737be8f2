<template>
	<div class="el-tiptap-editor__wrapper">
		<element-tiptap ref="tiptapRef" v-model:content="content" :locale="zh" :readonly="readonly" :extensions="extensions" @onUpdate="updateContent" />
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {
	ElementTiptap,
	Document,
	Text,
	Paragraph,
	Heading,
	Bold,
	Underline,
	Italic,
	Strike,
	Code,
	Link,
	Image,
	Iframe,
	Blockquote,
	BulletList,
	OrderedList,
	TaskList,
	TextAlign,
	Indent,
	HardBreak,
	HorizontalRule,
	CodeView,
	Fullscreen,
	History,
} from 'yun-element-tiptap';
import { storeToRefs } from 'pinia';

import codemirror from 'codemirror';
import 'codemirror/lib/codemirror.css'; // import base style
import 'codemirror/mode/xml/xml.js'; // language
import 'codemirror/addon/selection/active-line.js'; // require active-line.js
import 'codemirror/addon/edit/closetag.js'; // autoCloseTags

import zh from 'yun-element-tiptap/lib/locales/zh';

import { Session } from '/@/utils/storage';
import { useUserInfo } from '/@/stores/userInfo';
import request from '/@/utils/request';
import { getDownUrl } from '@/utils/downloadUrl';

const access_token = Session.getToken();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const tenantId = userInfos?.value?.tenantInfo?.id;

const fileUploadImage = (data) =>
	request({
		url: `${import.meta.env.VITE_VUE_PROXY_URL}/admin/sys-file/upload`,
		method: 'POST',
		headers: {
			Authorization: `Bearer ${access_token}`,
			'TENANT-ID': tenantId,
			'Content-Type': 'multipart/form-data',
		},
		data,
	});

// 编辑器的 extensions
// 它们将会按照你声明的顺序被添加到菜单栏和气泡菜单中
const extensions = [
	Document,
	Text,
	Paragraph,
	Heading.configure({ level: 5 }),
	Bold.configure({ bubble: true }),
	Underline.configure({ bubble: true }),
	Italic.configure({ bubble: true }),
	Strike.configure({ bubble: true }),
	Code,
	Link.configure({ bubble: true }),
	Image.configure({
		// 默认会把图片生成base64字符串和内容存储在一起，如果需要自定义图片上传
		uploadRequest(file) {
			// 如果接口要求Content-Type是multipart/form-data，则请求体必须使用FormData
			const formData = new FormData();
			formData.append('file', file);
			// 调用接口进行上传，拿到服务器返回的图片地址
			return fileUploadImage(formData).then((res) => {
				// 这个return是返回请求成功后拿到的url，赋值给富文本编辑器里面的img中的src属性
				return getDownUrl(res.data.data.url);
			});
		},
	}),
	Iframe,
	Blockquote,
	TextAlign,
	BulletList.configure({ bubble: true }),
	OrderedList.configure({ bubble: true }),
	TaskList,
	Indent,
	HardBreak,
	HorizontalRule.configure({ bubble: true }),
	CodeView.configure({
		codemirror,
		codemirrorOptions: {
			styleActiveLine: true,
			autoCloseTags: true,
		},
	}),
	Fullscreen,
	History,
];

const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	readonly: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['update:modelValue']);

const tiptapRef = ref();

watch(
	() => props.modelValue,
	(value) => {
		// HTML
		const isSame = tiptapRef.value?.editor.getHTML() === value;

		// JSON
		// const isSame = JSON.stringify(this.editor.getJSON()) === JSON.stringify(value)

		if (isSame) {
			return;
		}

		tiptapRef.value?.editor.commands.setContent(value, false);
	}
);

const content = ref('');
const updateContent = (value, editor) => {
	emit('update:modelValue', value);
};
</script>
