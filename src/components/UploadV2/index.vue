<template>
	<div class="attach-wrapper">
		<el-upload
			:file-list="fileList"
			:action="upLoadUrl"
			:headers="{
				...uploadHeader(),
			}"
			:before-upload="handleBeforeUpload"
			:on-success="handleSuccess"
			:disabled="disabled"
			multiple
			:limit="limit"
			:accept="props.accept"
		>
			<el-button v-show="!disabled" type="primary">
				<i class="yun-iconfont icon-upload" style="margin-right: 5px" />{{ $t('UploadV2.index.003348-0') }}
			</el-button>
			<template #tip>
				<div v-show="!disabled" class="el-upload__tip" style="color: #7e8694">
					{{ tips }}
				</div>
			</template>
			<template #file="{ file }">
				<div class="files-list">
					<div class="file-name">
						<i class="yun-iconfont icon-page" style="margin-right: 5px" />
						{{ file.name }}
					</div>
					<div style="display: flex; width: 60px">
						<i class="yun-iconfont icon-download color-primary" style="margin-right: 10px" @click="handleDown(file)" />
						<i v-show="!disabled" class="yun-iconfont icon-trash color-primary" @click="handleRemove(file)" />
					</div>
				</div>
			</template>
		</el-upload>
	</div>
</template>

<script setup>
import { toRefs, watch } from 'vue';
import { ElMessage } from 'yun-design';
import dayjs from 'dayjs';
import downloadUrlFile, { uploadHeader } from '@/utils/downloadUrl';

const upLoadUrl = `${import.meta.env.VITE_VUE_PROXY_URL}/admin/file/upload-v2?dir=file&fileType=*`;

const props = defineProps({
	disabled: {
		type: Boolean,
		default: false,
	},
	tips: {
		type: String,
		default: t('UploadV2.index.003348-1'),
	},
	accept: {
		type: String,
		default: '',
	},
	fileSize: {
		type: Number,
		default: 10,
	},
	files: {
		type: Array,
		default: () => [],
	},
	beforeUpload: {
		type: Function,
		default: () => true,
	},
	limit: {
		type: Number,
		default: 5,
	},
});

const emit = defineEmits(['remove', 'success']);

const fileList = computed(() => props.files || []);

const handleBeforeUpload = (file) => {
	let before = true;
	if (typeof props.beforeUpload === 'function') {
		before = props.beforeUpload(file);
	}
	if (!before) {
		return before;
	}
	const isLt10M = file.size / 1024 / 1024 < props.fileSize;
	const { accept } = props;
	if (accept && !(accept.includes(fileExtension) || accept.includes(`.${fileExtension}`))) {
		ElMessage.warning(t('UploadV2.index.003348-2', [accept.replaceAll(',', '/')]));
		return false;
	}
	if (!isLt10M) {
		ElMessage.warning(t('UploadV2.index.003348-3', [props.fileSize]));
		return false;
	}
	return isLt10M;
};

const handleSuccess = (res, file, files) => {
	emit('success', res, file, files);
};

const handleDown = (file) => {
	downloadUrlFile(file.url, file.name);
};

const getUrl = (file) => {
	let { url } = file;
	if (!url) {
		url = file?.response?.data?.url;
	}
	return url;
};

const handleRemove = (file) => {
	emit('remove', file);
};
</script>
<style lang="scss" scoped>
.attach-wrapper {
	.files-list {
		display: flex;
		justify-content: space-between;
		border-radius: 4px;
		font-size: 12px;
		padding: 0 4px;
		&:hover {
			background: #f0f2f5;
			.icon-trash,
			.icon-download {
				display: block;
			}
		}
		.file-name {
			display: flex;
			align-items: center;
			color: #1c2026;
			.icon-page {
				color: #505762;
			}
		}
		.icon-trash,
		.icon-download {
			color: #7e8694;
			cursor: pointer;
			display: none;
		}
	}
}
.color-primary {
	color: var(--el-color-primary) !important;
}
</style>
