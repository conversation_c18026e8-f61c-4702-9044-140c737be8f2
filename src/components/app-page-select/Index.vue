<template>
	<div>
		<el-form-item :label="`${$t('app-page-select.Index.331301-0')}`">
			<el-radio-group v-model="form.linkType">
				<el-radio :label="'inner'">{{ $t('app-page-select.Index.331301-1') }}</el-radio>
				<el-radio :label="'outer'">{{ $t('app-page-select.Index.331301-2') }}</el-radio>
			</el-radio-group>
		</el-form-item>
		<div v-if="form.linkType === 'inner'">
			<el-select
				style="float: left; width: 32% !important"
				:disabled="isGoodsPage"
				filterable
				v-model="form.page"
				:placeholder="`${$t('app-page-select.Index.331301-3')}`"
				@change="onChangePage"
			>
				<el-option v-for="(item, index) in pageUrls.pages" :key="index" :label="item.name" :value="item.url">
					<div style="margin: 6px 0">
						<div style="line-height: 1">{{ item.name }}</div>
						<div style="line-height: 1; color: #909399; font-size: 12px">{{ item.url }}</div>
					</div>
				</el-option>
			</el-select>
			<el-select
				v-model="form.categoryId"
				style="float: left; width: 32% !important; margin-left: 5px"
				:placeholder="`${$t('app-page-select.Index.331301-4')}`"
				v-if="showCategory"
				@change="handleCategoryChange"
			>
				<el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
			</el-select>
			<!-- <el-select
				v-if="!isArticleInfo"
				style="float: left; width: 32% !important; margin-left: 5px"
				:disabled="shopDisabled"
				filterable
				v-model="form.shopId"
				placeholder="请选择店铺"
				@change="onChangeShop"
			>
				<el-option v-for="(item, index) in shopList" :key="index" :label="item.name" :value="item.id"></el-option>
			</el-select> -->

			<el-select
				v-if="!isArticleInfo"
				style="float: left; width: 33% !important; margin-left: 5px"
				:disabled="spuDisabled"
				filterable
				v-model="form.spuId"
				:placeholder="`${$t('app-page-select.Index.331301-5')}`"
				@change="onChangeSpu"
			>
				<el-option v-for="(item, index) in spuList" :key="index" :label="item.name" :value="item.id"></el-option>
			</el-select>
			<el-select
				v-if="isArticleInfo"
				style="float: left; width: 66% !important; margin-left: 5px"
				filterable
				v-model="form.articleInfoId"
				:placeholder="`${$t('app-page-select.Index.331301-6')}`"
				@change="onChangeArticle"
			>
				<el-option v-for="(item, index) in articleInfoList" :key="index" :label="item.articleTitle" :value="item.id"></el-option>
			</el-select>
		</div>
		<el-input
			style="margin-top: 5px"
			:disabled="form.linkType === 'inner'"
			:placeholder="`${$t('app-page-select.Index.331301-7')}`"
			v-model="pageTemp"
		></el-input>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import request from '/@/utils/request';
// import * as shopinfo from '@/api/mall/shopinfo';
import * as goodsspu from '@/api/mall/goodsspu';
import * as bargaininfo from '@/api/mall/bargaininfo';
import * as grouponinfo from '@/api/mall/grouponinfo';
import * as seckillinfo from '@/api/mall/seckillinfo';
import { pageUrls as configPageUrls } from './pageUrls';
import { fetchTree as getGoodsCategory } from '@/api/mall/goodscategory';
import * as articleinfo from '@/api/mall/articleinfo';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';

const stores = useUserInfo();
const { shopInfo } = storeToRefs(stores);
const shopId = shopInfo.value.id;

const props = defineProps({
	curItem: {
		type: Object,
		default: () => {},
	},
	page: {
		type: String,
		default: () => '',
	},
	categoryId: {
		type: String,
		default: () => '',
	},
	clientType: {
		type: String,
		default: () => '',
	}, // 当前自定义页面类型
	isGoodsPage: {
		//是否只选择商品
		type: Boolean,
		default: () => false,
	},
	isRefresh: {
		//是否根据url自动刷新，用于公众号选择链接时
		type: Boolean,
		default: () => false,
	},
	linkType: {
		type: String,
		default: () => '',
	},
});

const emit = defineEmits(['change', 'changeGoods', 'update:linkType']);

const pageUrls = ref(JSON.parse(JSON.stringify(configPageUrls)));
const curType = ref('');
const pageTemp = ref(''); //临时展示的url
const shopDisabled = ref(true);
const spuDisabled = ref(true);
const isArticleInfo = ref(false); // 文章 pages/article/article-info/index'
const articleInfoList = ref([]);
const shopList = ref([]); // 店铺列表
const spuList = ref([]); // 店铺的商品列表
const form = ref({
	page: '',
	linkType: '',
	shopId,
	spuId: '',
	articleInfoId: '',
	categoryId: '',
});
const categoryList = ref([]);
const showCategory = ref(false);

const getBargaininfo = () => {
	//获取店铺所有 砍价的商品
	bargaininfo
		.getPage({ shopId })
		.then((response) => {
			spuList.value = response.goodsDetailPage.records;
		})
		.catch(() => {});
};

const getBargaininfoDetail = (id) => {
	//获取 砍价的商品详情,用于回显
	bargaininfo
		.getObj(id)
		.then((response) => {
			if (response.data) {
				getBargaininfo();
			}
		})
		.catch(() => {});
};

const getGrouponinfo = () => {
	//获取店铺所有 拼团的商品
	grouponinfo
		.getPage({ shopId })
		.then((response) => {
			spuList.value = response.data.records;
		})
		.catch(() => {});
};

const getGrouponinfoDetail = (id) => {
	//获取店铺所有 拼团的商品详情，用于回显
	grouponinfo
		.getObj(id)
		.then((response) => {
			if (response.data) {
				getGrouponinfo();
			}
		})
		.catch(() => {});
};

const getGoodsSpu = () => {
	//获取店铺所有商品
	request({
		url: '/mall/goodsspu/list?shopId=' + shopId,
		method: 'get',
		params: {},
	})
		.then((response) => {
			spuList.value = response;
		})
		.catch(() => {});
};

const getSeckillinfo = () => {
	//获取店铺所有 秒杀 的商品
	seckillinfo
		.getPage({ shopId })
		.then((response) => {
			spuList.value = response.data.records;
		})
		.catch(() => {});
};

const getSeckillDetail = (id) => {
	//获取 秒杀 详情，用于回显
	seckillinfo
		.getObj(id)
		.then((response) => {
			if (response.data) {
				getSeckillinfo();
			}
		})
		.catch(() => {});
};

const getGoodsSpuDetail = (id) => {
	//获取商品详情，用于回显
	goodsspu
		.getObj(id)
		.then((response) => {
			if (response.data) {
				getGoodsSpu();
			}
		})
		.catch(() => {});
};

const initPage = () => {
	if (props.page) {
		let pageTempT = JSON.parse(JSON.stringify(props.page));
		pageTemp.value = pageTempT;
		if (props.page.indexOf('http') != -1) {
			pageTempT = pageTempT.substring(props.page.indexOf('/pages/'), props.page.length);
		}
		let appIdIndex = pageTempT.indexOf('app_id');
		if (appIdIndex != -1) {
			pageTempT = pageTempT.substring(0, appIdIndex - 1);
		}
		if (pageTempT.indexOf('id') != -1) {
			//表示是详情页
			let urlTemp = pageTempT.substring(0, pageTempT.indexOf('id=') + 3);
			let idTemp = pageTempT.substring(pageTempT.indexOf('id=') + 3, pageTempT.length);
			form.value.page = urlTemp;
			if (props.page.indexOf('/pages/shop/shop-detail/index?id=') != -1) {
				//如果是店铺详情页，那么就把选择商品禁用
				form.value.shopId = idTemp;
				shopDisabled.value = false;
				spuDisabled.value = true;
				curType.value = pageUrls.value.types.shopDetailPage;
			} else {
				form.value.spuId = idTemp;
				pageUrls.value.pages.forEach((item, index) => {
					if (item.url == urlTemp) {
						//判断url类型
						curType.valye = item.type;
						return; //跳出循环
					}
				});
				if (idTemp) {
					switch (curType.value) {
						case pageUrls.value.types.bargainDetailPage:
							getBargaininfoDetail(idTemp);
							break;
						case pageUrls.value.types.grouponDetailPage:
							getGrouponinfoDetail(idTemp);
							break;
						case pageUrls.value.types.goodsDetailPage:
							getGoodsSpuDetail(idTemp);
							break;
						case pageUrls.value.types.seckillDetailPage:
							getSeckillDetail(idTemp);
							break;
					}
				}
				shopDisabled.value = false;
				spuDisabled.value = false;
			}
		} else {
			shopDisabled.value = true;
			spuDisabled.value = true;
			form.value.page = pageTemp;
		}
	} else {
		if (props.isGoodsPage) {
			form.value.page = '/pages/goods/goods-detail/index?id=';
			this.onChangePage(form.value.page);
		}
	}
};

// const getShopList = () => {
// 	//获取所有店铺
// 	shopinfo
// 		.getList({})
// 		.then((response) => {
// 			shopList.value = response.data;
// 		})
// 		.catch(() => {});
// };

const handleCategoryChange = (categoryId) => {
	curItem.value.categoryId = categoryId;
};

const getCategoryList = async () => {
	const res = await getGoodsCategory();
	categoryList.value = res.data;
	// 显示category
	showCategory.value = true;
};

const setPagePath = (url) => {
	pageTemp.value = url;
};

const onChangeShop = (value) => {
	//更改店铺
	if (value) {
		form.value.spuId = '';
		pageTemp.value = form.value.page + form.value.spuId;
		if (curType.value != pageUrls.value.types.shopDetailPage) {
			if (curType.value == pageUrls.value.types.bargainDetailPage) {
				//如果是 砍价详情页面 取砍价的商品列表
				getBargaininfo();
			} else if (curType.value == pageUrls.value.types.grouponDetailPage) {
				//如果是 拼团活动详情
				getGrouponinfo();
			} else if (curType.value == pageUrls.value.types.seckillDetailPage) {
				//如果是 秒杀 详情
				getSeckillinfo();
			} else if (curType.value == pageUrls.value.types.goodsDetailPage) {
				//如果是 商品详情
				getGoodsSpu();
			}
		} else {
			//如果是店铺
			pageTemp.value = form.value.page + form.value.shopId;
		}
		emit('change', pageTemp.value);
	}
};

const onChangePage = (value) => {
	if (value) {
		form.value.spuId = '';
		form.value.articleInfoId = '';
		if (value == '/pages/article/article-info/index?id=') {
			//文章单独判断
			isArticleInfo.value = true;
			if (articleInfoList.value.length == 0) {
				articleinfo
					.getPage({})
					.then((response) => {
						articleInfoList.value = response.data.records;
					})
					.catch(() => {});
			}
		} else {
			isArticleInfo.value = false;
			pageUrls.value.pages.forEach((item, index) => {
				if (item.url == value) {
					//判断url类型
					curType.value = item.type;
					if (item.type != pageUrls.value.types.singlePage) {
						//如果不是单页面，就表示可以选店铺
						shopDisabled.value = false;
						if (item.type != pageUrls.value.types.shopDetailPage) {
							//如果不是店铺详情就可以选商品
							spuDisabled.value = false;
						} else {
							spuDisabled.value = true;
						}
						onChangeShop(shopId);
					} else {
						shopDisabled.value = true;
						spuDisabled.value = true;
					}
					return;
				}
			});
		}
		pageTemp.value = value;
		emit('change', pageTemp.value);
	}
};

const onChangeSpu = (value) => {
	//更改店铺商品
	if (value) {
		pageTemp.value = form.value.page + form.value.spuId;
		emit('change', pageTemp.value);
		if (props.isGoodsPage) {
			//如果只选择商品时，可能需要商品id
			const goods = spuList.value.find((_item) => _item.id === form.value.spuId);
			emit('changeGoods', goods);
		}
	}
};

const onChangeArticle = (value) => {
	//文章
	if (value) {
		pageTemp.value = form.value.page + form.value.articleInfoId;
		emit('change', pageTemp.value);
	}
};

onMounted(() => {
	if (props.clientType === 'MA') {
		// 如果是小程序的首页，那么可以选择小程序直播列表页面
		pageUrls.value.pages.push({
			name: t('app-page-select.Index.331301-8'),
			url: '/pages/live/room-list/index',
			type: t('app-page-select.Index.331301-9'),
		});
	}
	initPage();
	// getShopList(); // 加载所有店铺数据
	form.value.linkType = props.linkType;
});

watch(
	() => props.categoryId,
	(val) => {
		form.categoryId = val;
	},
	{ immediate: true }
);

watch(
	() => form.linkType,
	() => {
		emit('update:linkType', val);
	}
);

watch(
	() => props.page,
	(val, oldVal) => {
		// 商品分类
		if (val === '/pages/goods/goods-category/index') {
			// 获取category
			getCategoryList();
		} else {
			showCategory.value = false;
		}
		if (val != oldVal && props.isRefresh) {
			initPage();
		}
	},
	{ immediate: true }
);

watch(
	() => props.linkType,
	(val) => {
		form.value = {
			page: '',
			linkType: val,
			shopId,
			spuId: '',
			articleInfoId: '',
			categoryId: '',
		};
		pageTemp.value = '';
	}
);

watch(pageTemp, () => {
	emit('change', pageTemp.value);
});

defineExpose({
	setPagePath,
});
</script>

<style scoped></style>
