export const pageUrls = {
	types: {
		//类型
		singlePage: t('app-page-select.pageUrls.517177-0'),
		// bargainDetailPage: '砍价详情页面',
		// grouponDetailPage: '拼团详情页面',
		goodsDetailPage: t('app-page-select.pageUrls.517177-1'),
		// shopDetailPage: '店铺详情页面',
	},
	pages: [
		{
			name: t('app-page-select.pageUrls.517177-2'),
			url: '/pages/home/<USER>',
			type: t('app-page-select.pageUrls.517177-0'),
		},

		{
			name: t('app-page-select.pageUrls.517177-1'),
			url: '/pages/goods/goods-detail/index?id=',
			type: t('app-page-select.pageUrls.517177-1'),
			shopShow: true,
		},
		// {
		// 	name: '店铺列表页面',
		// 	url: '/pages/shop/shop-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// },
		// {
		// 	name: '店铺详情页面',
		// 	url: '/pages/shop/shop-detail/index?id=',
		// 	type: '店铺详情页面',
		// },
		// {
		// 	name: '秒杀列表页面',
		// 	url: '/pages/seckill/seckill-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// 	shopShow: true,
		// 	shopList: true,
		// },
		// {
		// 	name: '砍价活动列表页面',
		// 	url: '/pages/bargain/bargain-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// 	shopShow: true,
		// 	shopList: true,
		// },
		// {
		// 	name: '砍价活动商品详情',
		// 	url: '/pages/bargain/bargain-detail/index?id=',
		// 	type: '砍价详情页面',
		// 	shopShow: true,
		// },
		// {
		// 	name: '拼团活动列表页面',
		// 	url: '/pages/groupon/groupon-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// 	shopShow: true,
		// 	shopList: true,
		// },
		// {
		// 	name: '拼团活动商品详情',
		// 	url: '/pages/groupon/groupon-detail/index?id=',
		// 	type: '拼团详情页面',
		// 	shopShow: true,
		// },
		// {
		// 	name: '领取优惠券页面',
		// 	url: '/pages/coupon/coupon-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// 	shopShow: true,
		// 	shopList: true,
		// },
		// {
		// 	name: '我的积分',
		// 	url: '/pages/user/user-points-record/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// },
		{
			name: t('app-page-select.pageUrls.517177-3'),
			url: '/pages/goods/goods-category/index',
			type: t('app-page-select.pageUrls.517177-0'),
		},
		{
			name: t('app-page-select.pageUrls.517177-4'),
			url: '/pages/user/user-collect/index',
			type: t('app-page-select.pageUrls.517177-0'),
		},
		{
			name: t('app-page-select.pageUrls.517177-5'),
			url: '/pages/order/order-list/index',
			type: t('app-page-select.pageUrls.517177-0'),
		},
		// {
		// 	name: '文章列表',
		// 	url: '/pages/article/article-list/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// },
		// {
		// 	name: '文章详情',
		// 	url: '/pages/article/article-info/index?id=',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// 	shopShow: true,
		// },
		// {
		// 	name: '积分签到',
		// 	url: '/pages/signrecord/signrecord-info/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// },
		// {
		// 	name: '分销中心',
		// 	url: '/pages/distribution/distribution-center/index',
		// 	type: t('app-page-select.pageUrls.517177-0'),
		// },
	],
};
