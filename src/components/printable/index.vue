<template>
	<el-button type="text" @click="show">{{ $t('printable.index.089233-0') }}</el-button>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getPrintId } from '@/api/common/printable';
import { ElMessage } from 'yun-design';
import { useUserInfo } from '/@/stores/userInfo';
import { Session } from '/@/utils/storage';
import { storeToRefs } from 'pinia';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const tenantId = userInfos?.value?.tenantInfo?.id;
const access_token = Session.getToken();

const props = defineProps({
	props: {
		type: Object,
		default: () => ({}),
	},
	menuCode: {
		type: String,
		default: '',
	},
	params: {
		type: Object,
		default: () => ({}),
	},
});

const urlPath = ref('');
const options = ref({ id: 'id', customerId: 'customerId' });
const menuType = ref([
	{
		groupName: '仓库管理(商流)',
		name: t('printable.index.089233-3'),
		code: 'saas_warhouse_in',
		groupCode: 'sl_warhouse_management',
	},
	{
		groupName: '仓库管理(商流)',
		name: t('printable.index.089233-4'),
		code: 'saas_warhouse_out',
		groupCode: 'sl_warhouse_management',
	},
	{
		groupName: t('printable.index.089233-5'),
		name: t('printable.index.089233-6'),
		code: 'saas_purchase_order',
		groupCode: 'sl_purchase_management',
	},
	{
		groupName: t('printable.index.089233-5'),
		name: t('printable.index.089233-7'),
		code: 'saas_purchase_returned_order',
		groupCode: 'sl_purchase_management',
	},
	{
		groupName: t('printable.index.089233-8'),
		name: t('printable.index.089233-9'),
		code: 'saas_sale_order',
		groupCode: 'sl_sale_management',
	},
	{
		groupName: t('printable.index.089233-8'),
		name: t('printable.index.089233-10'),
		code: 'saas_returned_order',
		groupCode: 'sl_sale_management',
	},
]);

onMounted(() => {
	options.value = Object.assign(options.value, props.props);
});

const show = async () => {
	try {
		const { data } = await getPrintId({ code: props.menuCode, relationId: props.params[options.value.customerId] ?? '' });
		const query = {
			token: `${access_token}:${tenantId}`,
			selectId: props.params[options.value.id],
		};
		let queryArray = [];
		for (let p in query) {
			queryArray.push(`${p}=${query[p]}`);
		}
		if (data) {
			urlPath.value = encodeURI(`${import.meta.env.VITE_VUE_PROXY_URL}/jimu/jmreport/view/${data}?${queryArray.join('&')}`);
			window.open(urlPath.value);
		} else {
			ElMessage.warning(t('printable.index.089233-11'));
		}
	} catch (err) {
		ElMessage.warning(t('printable.index.089233-11'));
	}
};
</script>
