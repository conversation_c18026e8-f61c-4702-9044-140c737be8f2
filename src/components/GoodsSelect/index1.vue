<template>
	<!--	<el-autocomplete v-if="!isButton" v-model="state.modelValue" style="width: 100%" :fetch-suggestions="fetchGoodsList" placeholder="请输入" @select="handleSelect">-->
	<!--		<template #default="{ item }">-->
	<!--			<div class="flex panel align-center">-->
	<!--				&lt;!&ndash; 商品图片 &ndash;&gt;-->
	<!--				<el-image style="width: 30px; height: 30px; border-radius: 6px" :src="item.picUrl" fit="fill"></el-image>-->
	<!--				&lt;!&ndash; 商品 &ndash;&gt;-->
	<!--				<div class="name">{{ item.spuName }}</div>-->
	<!--				&lt;!&ndash; 商品规格 &ndash;&gt;-->
	<!--				<div class="spec">{{ item.specInfo }}</div>-->
	<!--			</div>-->
	<!--		</template>-->
	<!--		<template #append>-->
	<!--			<el-button :icon="Plus" primary @click="showGoodsDialog"></el-button>-->
	<!--		</template>-->
	<!--	</el-autocomplete>-->
	<el-button type="primary" @click="showGoodsDialog">选择商品</el-button>
	<yun-drawer
		v-model="state.visible"
		title="商品选择"
		size="X-large"
		append-to-body
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:show-cancel-button="true"
		@confirm="confirmHandler"
	>
		<yun-pro-table
			v-if="state.visible"
			ref="tableRef"
			:global-selection="true"
			selection-key="id"
			:batch-fields="[]"
			v-model:pagination="state.page"
			v-model:selected="state.selection"
			v-model:searchData="state.searchData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="fetchTableData"
			:tableEvents="{
				'sort-change': sortChange,
			}"
		>
			<template #t_empty>
				<div>暂无数据</div>
			</template>
		</yun-pro-table>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { reactive, toRefs, onMounted } from 'vue';
import { ElNotification } from 'yun-design';
import { omit } from 'lodash';
import { getGoodscategoryTree } from '@/views/mall/api';
import { getPage } from '/@/api/mall/goodsspu';

const props = defineProps({
	modelValue: {
		type: Array,
		default: () => [],
	},
	params: {
		type: Object,
		default: () => {
			return {};
		},
	},
	isButton: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['change', 'update:modelValue']);

const state = reactive({
	query: '',
	loading: false,
	page: {
		total: 0, // 总页数
		page: 1, // 当前页数
		size: 20, // 每页显示多少条
	},
	searchData: {},
	selection: [],
	tableRef: null,
	visible: false,
	categorySecondTree: [],
	categorySecondShopTree: [],
});

const { tableRef } = toRefs(state);

const fetchCategorySecondTree = async () => {
	const res = await getGoodscategoryTree();
	state.categorySecondTree = res?.data || [];
};

const searchFields = computed(() => [
	{
		label: '商品',
		prop: 'name',
	},
	{
		label: '商城类目',
		prop: 'categoryId',
		component: 'el-cascader',
		componentAttrs: {
			clearable: true,
			filterable: true,
			props: {
				value: 'id',
				label: 'name',
				children: 'children',
				checkStrictly: true,
			},
			options: state.categorySecondTree,
			placeholder: '请选择商品分类',
		},
	},
	{
		label: '商品编码',
		prop: 'spuCode',
	},
	{
		label: '规格类型',
		prop: 'specType',
		component: 'el-select',
		options: [
			{
				label: '统一规格',
				value: '0',
			},
			{
				label: '多规格',
				value: '1',
			},
		],
	},
	{
		label: '是否上架',
		prop: 'shelf',
		component: 'el-select',
		options: [
			{
				label: '上架',
				value: 1,
			},
			{
				label: '下架',
				value: 0,
			},
		],
	},
	{
		label: '积分赠送',
		prop: 'pointsGiveSwitch',
		component: 'el-select',
		options: [
			{
				label: '开启',
				value: '1',
			},
			{
				label: '关闭',
				value: '0',
			},
		],
	},
	{
		label: '积分抵扣',
		prop: 'pointsDeductSwitch',
		component: 'el-select',
		options: [
			{
				label: '开启',
				value: '1',
			},
			{
				label: '关闭',
				value: '0',
			},
		],
	},
]);

const tableColumns = [
	{
		prop: 'selection',
		type: 'selection',
		width: 50,
		fixed: 'left',
	},
	{
		label: '商品',
		prop: 'name',
	},
	{
		label: '商品图片',
		prop: 'picUrl',
		render: ({ row }) => {
			const { picUrls } = row;
			if (picUrls) {
				return (
					<el-image
						style="width: 60px; height: 60px; border-radius: 6px"
						src={picUrls}
						preview-teleported={true}
						previewSrcList={[picUrls]}
						fit="fill"
					/>
				);
			}
			return <span>-</span>;
		},
	},
	{
		label: '商城类目',
		prop: 'categoryId',
		formatter(data) {
			let str = '';
			const { categoryFirst, categorySecond } = data;
			if (categoryFirst && categorySecond) {
				const categoryFirstData = state.categorySecondTree.find((item) => item.id === categoryFirst);
				str = categoryFirstData?.name;
				if (categoryFirstData?.children) {
					const categorySecondData = categoryFirstData.children.find((item) => item.id === categorySecond);
					str += `/${categorySecondData?.name}`;
				}
			}
			return str || '-';
		},
	},
	{
		label: '卖点',
		prop: 'sellPoint',
		sortable: 'custom',
	},
	{
		label: '价位',
		prop: 'price',
		render({ row }) {
			return <div class="text-red-500">{row.priceDown ? `￥${row.priceDown}` : '-'}</div>;
		},
	},
	{
		label: '商品编码',
		prop: 'spuCode',
		sortable: 'custom',
	},
	{
		label: '规格类型',
		prop: 'specType',
		enums: [
			{
				label: '统一规格',
				value: '0',
			},
			{
				label: '多规格',
				value: '1',
			},
		],
	},
	{
		label: '虚拟销量',
		prop: 'saleNum',
		sortable: 'custom',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		sortable: 'custom',
	},
	{
		label: '更新时间',
		prop: 'updateTime',
		sortable: 'custom',
	},
	{
		label: '是否上架',
		prop: 'shelf',
		sortable: 'custom',
		enums: [
			{
				label: '下架',
				value: '0',
			},
			{
				label: '上架',
				value: '1',
			},
		],
	},
	{
		label: '积分赠送',
		prop: 'pointsGiveSwitch',
		sortable: 'custom',
		enums: [
			{
				label: '开启',
				value: '1',
			},
			{
				label: '关闭',
				value: '0',
			},
		],
	},
	{
		label: '积分抵扣',
		prop: 'pointsDeductSwitch',
		sortable: 'custom',
		enums: [
			{
				label: '开启',
				value: '1',
			},
			{
				label: '关闭',
				value: '0',
			},
		],
	},
	{
		label: '审核状态',
		prop: 'verifyStatus',
		sortable: 'custom',
		enums: [
			{
				label: '审核中',
				value: '0',
			},
			{
				label: '审核通过',
				value: '1',
			},
			{
				label: '审核不通过',
				value: '2',
			},
		],
	},
];

const sortChange = ({ prop, order }) => {
	const propBean = prop ? prop.replace(/([A-Z])/g, '_$1').toLowerCase() : '';
	if (order === 'ascending') {
		state.searchData[`ascs`] = propBean;
		delete state.searchData[`descs`];
	} else {
		state.searchData[`descs`] = propBean;
		delete state.searchData[`ascs`];
	}
	nextTick(() => {
		tableRef.value.getData();
	});
};

const beforeFn = () => {
	const { requiredSupplier, supplierId } = props.params;
	if (requiredSupplier && !supplierId) {
		ElNotification.warning('请选择供应商');
		return false;
	}
	return true;
};

const showGoodsDialog = () => {
	if (!beforeFn()) {
		return;
	}
	state.selection = [...props.modelValue] || [];
	state.page.page = 1;
	state.visible = true;
};

const fetchTableData = async ({ searchData, pagination }) => {
	const { categoryId, ...rest } = searchData;
	const params = {
		...rest,
		...omit(props.params, ['requiredSupplier']),
	};
	if (categoryId && categoryId.length) {
		params.categoryFirst = categoryId[0];
		params.categorySecond = categoryId[1];
	}
	const { page: p, size } = pagination;
	const res = await getPage({ ...params, current: p, size });
	return res?.data || [];
};

const confirmHandler = () => {
	const list = state.selection;
	if (!list || !list.length) {
		ElNotification({
			message: '请选择商品！',
			type: 'warning',
		});
		return;
	}
	emit('change', [...list]);
	emit('update:modelValue', [...list]);
	state.visible = false;
};

onMounted(() => {
	fetchCategorySecondTree();
});
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
}
.panel {
	margin-bottom: 10px;
}
.align-center {
	align-items: center;
}
.name {
	margin-left: 8px;
	font-size: 14px;
	color: #666;
}

.spec {
	margin-left: 12px;
	font-size: 12px;
	color: #999;
}
</style>
