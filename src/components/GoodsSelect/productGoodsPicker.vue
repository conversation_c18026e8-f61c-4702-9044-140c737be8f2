<template>
	<el-autocomplete
		ref="completeRef"
		v-model="state.modelValue"
		style="width: 100%"
		trigger-on-focus
		:fetch-suggestions="fetchGoodsList"
		:placeholder="`${$t('GoodsSelect.index.358817-0')}`"
		@blur="handleBlur"
		@select="handleSelect"
	>
		<template #default="{ item }">
			<div class="flex panel align-center">
				<div class="name">{{ item.spuName }}</div>
				<div class="spec">{{ item.skuUnitName }}</div>
			</div>
		</template>
		<template v-if="isMultiple" #append>
			<el-button :icon="Plus" primary @click="showGoodsDialog"></el-button>
		</template>
	</el-autocomplete>
	<yun-drawer
		v-model="state.visible"
		:title="$t('GoodsSelect.index.358817-1')"
		size="X-large"
		append-to-body
		:confirm-button-text="$t('GoodsSelect.index.358817-2')"
		:cancel-button-text="$t('GoodsSelect.index.358817-3')"
		:show-cancel-button="true"
		@confirm="confirmHandler"
	>
		<yun-pro-table
			v-if="state.visible"
			ref="tableRef"
			v-model:pagination="state.page"
			v-model:selected="state.selection"
			:search-fields="searchFields"
			v-model:tableData="tableData"
			:table-columns="tableColumns"
			:remote-method="fetchTableData"
			:table-props="{
				'show-summary': false,
				'sum-text': ' ',
				spanMethod: handleSpanMethod,
			}"
		>
			<template #t_empty>
				<div>{{ $t('GoodsSelect.index.358817-4') }}</div>
			</template>
		</yun-pro-table>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { reactive, toRefs, nextTick, onMounted, watch } from 'vue';
import { ElNotification } from 'yun-design';
import { Plus } from '@yun-design/icons-vue';
import { omit } from 'lodash';
import { getPage } from './api.js';

const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	params: {
		type: Object,
		default: () => {
			return {};
		},
	},
	isMultiple: {
		type: Boolean,
		default: () => {
			return true;
		},
	},
});

const emit = defineEmits(['select', 'update:modelValue']);

const tableData = ref([]);
const state = reactive({
	modelValue: props.modelValue,
	query: '',
	page: {
		total: 0, // 总页数
		page: 1, // 当前页数
		size: 20, // 每页显示多少条
	},
	selection: [],
	tableRef: null,
	visible: false,
});

const { tableRef } = toRefs(state);

const searchFields = computed(() => [
	{
		label: t('GoodsSelect.productGoodsPicker.844980-0'),
		prop: 'skuCode',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.productGoodsPicker.844980-1'),
		},
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-2'),
		prop: 'spuName',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.productGoodsPicker.844980-3'),
		},
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-4'),
		prop: 'materialSkuCode',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.productGoodsPicker.844980-5'),
		},
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-6'),
		prop: 'materialSpuName',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.productGoodsPicker.844980-5'),
		},
	},
]);

const tableColumns = [
	{
		prop: 'selection',
		type: 'selection',
		width: 50,
		fixed: 'left',
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-2'),
		prop: 'spuName',
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-7'),
		prop: 'skuUnitName',
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-6'),
		prop: 'materialSpuName',
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-8'),
		prop: 'materialSkuUnitName',
	},
	{
		label: t('GoodsSelect.productGoodsPicker.844980-9'),
		prop: 'quantity',
		render: ({ row }) => {
			let { quantity: rate } = row;
			if (rate >= 1) {
				return `1: ${rate}`;
			} else if(rate) {
				let temp = 1;
				while (rate < 1) {
					rate *= 10;
					temp *= 10;
				}
				return `${temp}: ${rate}`;
			} else {
				return '--'
			}
		},
	},
];

const formatData = (list = []) => {
	const records = list;
	const obj = {};
	records.forEach((item) => {
		const { skuCode } = item;
		if (obj[skuCode]) {
			obj[skuCode] = ++obj[skuCode];
		} else {
			obj[skuCode] = 1;
		}
	});
	const keys = Object.keys(obj);
	keys.forEach((key) => {
		const first = records.find((item) => item.skuCode === key);
		first.rowspan = obj[key];
	});
	return records;
};

const handleBlur = () => {
	nextTick(() => {
		state.modelValue = props.modelValue;
	});
};

const handleSelect = (value) => {
	state.modelValue = value.spuName;
	emit('select', [value], 'single');
};

const beforeFn = () => {
	const { requiredSupplier, supplierId, requiredWarehouse, warehouseId } = props.params;
	if (requiredSupplier && !supplierId) {
		ElNotification.warning(t('GoodsSelect.index.358817-14'));
		return false;
	}
	if (requiredWarehouse && !warehouseId) {
		ElNotification.warning(t('GoodsSelect.productGoodsPicker.844980-10'));
		return false;
	}
	return true;
};

const showGoodsDialog = () => {
	if (!beforeFn()) {
		return;
	}
	state.selection = [];
	state.visible = true;
};

const formatSelection = (selection) => {
	const list = [];
	tableData.value.forEach((item) => {
		if (selection.find((inner) => inner.skuCode === item.skuCode)) {
			list.push(item);
		}
	});
	return list;
};

const completeRef = ref(null);

const fetchGoodsList = async (query, cb) => {
	if (!beforeFn()) {
		cb([]);
		return;
	}
	try {
		let searchKeywords = query;
		if (props.modelValue === query) {
			searchKeywords = '';
		}
		const res = await getPage({
			searchKeywords,
			...omit(props.params, ['requiredSupplier', 'requiredWarehouse']),
		});
		const list = formatData(res?.data?.records || []);
		cb(list);
	} catch (err) {
		Promise.reject(err);
	}
};

const fetchTableData = async ({ searchData, pagination }) => {
	const { categorySecond, ...rest } = searchData;
	const params = {
		...rest,
		...omit(props.params, ['requiredSupplier', 'requiredWarehouse']),
	};
	if (categorySecond && categorySecond.length) {
		const len = categorySecond.length;
		params.categorySecond = categorySecond[len - 1];
	}
	const { page: p, size } = pagination;
	const res = await getPage({ ...params, current: p, size });
	res.data.records = formatData(res.data.records);
	return res.data;
};

const confirmHandler = () => {
	const list = state.selection;
	if (!list || !list.length) {
		ElNotification({
			message: t('GoodsSelect.index.358817-15'),
			type: 'warning',
		});
		return;
	}
	emit('select', formatSelection([...list]), 'multiple');
	state.visible = false;
};

const handleSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
	if (['selection', 'spuName', 'skuUnitName'].includes(column.property)) {
		return {
			rowspan: row.rowspan || 0,
			colspan: 1,
		};
	}
};

watch(
	() => props.modelValue,
	(v) => {
		state.modelValue = v;
	}
);
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
}
.panel {
	margin-bottom: 10px;
}
.align-center {
	align-items: center;
}
.name {
	margin-left: 8px;
	font-size: 14px;
	color: #666;
}

.spec {
	margin-left: 12px;
	font-size: 12px;
	color: #999;
}
</style>
