<template>
	<el-autocomplete
		ref="completeRef"
		v-model="state.modelValue"
		style="width: 100%"
		trigger-on-focus
		:fetch-suggestions="fetchGoodsList"
		:placeholder="`${$t('GoodsSelect.index.358817-0')}`"
		@blur="handleBlur"
		@select="handleSelect"
	>
		<template #default="{ item }">
			<div class="flex panel align-center">
				<el-image style="height: 22px; border-radius: 6px" :src="item.picUrl" fit="fill"></el-image>
				<div class="name">{{ item.spuName }}</div>
				<div class="spec">{{ item.specInfo }}</div>
			</div>
		</template>
		<template v-if="isMultiple" #append>
			<el-button :icon="Plus" primary @click="showGoodsDialog"></el-button>
		</template>
	</el-autocomplete>
	<yun-drawer
		v-model="state.visible"
		:title="$t('GoodsSelect.index.358817-1')"
		size="X-large"
		append-to-body
		:confirm-button-text="$t('GoodsSelect.index.358817-2')"
		:cancel-button-text="$t('GoodsSelect.index.358817-3')"
		:show-cancel-button="true"
		@confirm="confirmHandler"
	>
		<div class="global-goods-table-container">
			<yun-pro-table
				v-if="state.visible"
				ref="tableRef"
				v-model:pagination="state.page"
				v-model:selected="state.selection"
				:search-fields="searchFields"
				:table-columns="tableColumns"
				:search-data="state.searchData"
				:remote-method="fetchTableData"
				auto-height
			>
				<template #t_empty>
					<div>{{ $t('GoodsSelect.index.358817-4') }}</div>
				</template>
			</yun-pro-table>
		</div>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { reactive, toRefs, nextTick, onMounted, watch } from 'vue';
import { ElNotification } from 'yun-design';
import { Plus } from '@yun-design/icons-vue';
import { omit } from 'lodash';
import { getGoodsList, getGoodscategoryTree } from '@/views/mall/api';

const props = defineProps({
	api: {
		type: Function,
		default: getGoodsList,
	},
	modelValue: {
		type: String,
		default: '',
	},
	params: {
		type: Object,
		default: () => {
			return {};
		},
	},
	isMultiple: {
		type: Boolean,
		default: () => {
			return true;
		},
	},
});

const emit = defineEmits(['select', 'update:modelValue']);

const state = reactive({
	modelValue: props.modelValue,
	query: '',
	page: {
		total: 0, // 总页数
		page: 1, // 当前页数
		size: 20, // 每页显示多少条
	},
	selection: [],
	tableRef: null,
	visible: false,
	categorySecondTree: [],
	searchData: {},
});

const { tableRef } = toRefs(state);

const fetchCategorySecondTree = async () => {
	const res = await getGoodscategoryTree();
	state.categorySecondTree = res?.data || [];
};

const fetchMultiUnit = async (list = []) => {
	const spuIds = list.map((item) => item.spuId);

	const { categorySecond, ...rest } = state.searchData;
	const params = {
		...rest,
		...omit(props.params, ['requiredSupplier', 'requiredWarehouse']),
	};
	if (categorySecond && categorySecond.length) {
		const len = categorySecond.length;
		params.categorySecond = categorySecond[len - 1];
	}
	const res = await props.api({ ...params, whetherPurchaseUnit: undefined, spuIds, current: 1, size: 500 });
	const rstList = res?.data?.records || [];
	rstList.forEach((item) => {
		const target = list.find((v) => v.spuId === item.spuId);
		target && (target.multiUnit?.length ? target.multiUnit.push(item) : (target.multiUnit = [item]));
	});
	return list;
};

const searchFields = computed(() => [
	{
		label: t('GoodsSelect.index.358817-5'),
		prop: 'skuCode',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.index.358817-6'),
		},
	},
	{
		label: t('GoodsSelect.index.358817-7'),
		prop: 'spuName',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.index.358817-16'),
		},
	},
	{
		label: t('GoodsSelect.index.358817-8'),
		prop: 'easyCode',
		component: 'el-input',
		componentAttrs: {
			placeholder: t('GoodsSelect.index.358817-17'),
		},
	},
	{
		label: t('GoodsSelect.index.358817-9'),
		prop: 'categorySecond',
		component: 'el-cascader',
		componentAttrs: {
			clearable: true,
			filterable: true,
			props: {
				value: 'id',
				label: 'name',
				children: 'children',
				checkStrictly: true,
			},
			options: state.categorySecondTree,
			placeholder: t('GoodsSelect.index.358817-10'),
		},
	},
]);

const tableColumns = [
	{
		prop: 'selection',
		type: 'selection',
		width: 50,
		fixed: 'left',
	},
	{
		label: t('GoodsSelect.index.358817-5'),
		prop: 'skuCode',
		formatter(data) {
			return data.skuCode || data.spuCode;
		},
	},
	{
		label: t('GoodsSelect.index.358817-11'),
		prop: 'picUrl',
		render: ({ row }) => {
			const { picUrl } = row;
			if (picUrl) {
				return <el-image style="width: 30px; height: 30px; border-radius: 6px" src={picUrl} fit="fill"></el-image>;
			}
			return <span>-</span>;
		},
	},
	{
		label: t('GoodsSelect.index.358817-9'),
		prop: 'categoryInfo',
	},
	{
		label: t('GoodsSelect.index.358817-7'),
		prop: 'spuName',
	},
	{
		label: t('GoodsSelect.index.358817-12'),
		prop: 'specInfo',
	},
	{
		label: t('GoodsSelect.index.358817-13'),
		prop: 'currentUnitName',
	},
];

const handleBlur = () => {
	nextTick(() => {
		state.modelValue = props.modelValue;
	});
};

const handleSelect = async (value) => {
	state.modelValue = value.spuName;
	const list = await fetchMultiUnit([value]);
	emit('select', list, 'single');
};

const beforeFn = () => {
	const { requiredSupplier, supplierId, requiredWarehouse, warehouseId } = props.params;
	if (requiredSupplier && !supplierId) {
		ElNotification.warning(t('GoodsSelect.index.358817-14'));
		return false;
	}
	if (requiredWarehouse && !warehouseId) {
		ElNotification.warning(t('GoodsSelect.index.702067-0'));
		return false;
	}
	return true;
};

const showGoodsDialog = () => {
	if (!beforeFn()) {
		return;
	}
	state.selection = [];
	state.visible = true;
};

const completeRef = ref(null);

const fetchGoodsList = async (query, cb) => {
	if (!beforeFn()) {
		cb([]);
		return;
	}
	try {
		let searchKeywords = query;
		if (props.modelValue === query) {
			searchKeywords = '';
		}
		const res = await props.api({
			searchKeywords,
			...omit(props.params, ['requiredSupplier', 'requiredWarehouse']),
			current: 1,
			size: 300,
		});
		const list = res?.data?.records || [];
		cb(list);
	} catch (err) {
		Promise.reject(err);
	}
};

const fetchTableData = async ({ searchData, pagination }) => {
	const { categorySecond, ...rest } = searchData;
	const params = {
		...rest,
		...omit(props.params, ['requiredSupplier', 'requiredWarehouse']),
	};
	if (categorySecond && categorySecond.length) {
		const len = categorySecond.length;
		params.categorySecond = categorySecond[len - 1];
	}
	const { page: p, size } = pagination;
	const res = await props.api({ ...params, current: p, size });
	return res?.data || [];
};

const confirmHandler = async () => {
	const list = state.selection;
	if (!list || !list.length) {
		ElNotification({
			message: t('GoodsSelect.index.358817-15'),
			type: 'warning',
		});
		return;
	}
	const rstList = await fetchMultiUnit(list);
	emit('select', rstList, 'multiple');

	state.visible = false;
};

onMounted(() => {
	fetchCategorySecondTree();
});

watch(
	() => props.modelValue,
	(v) => {
		state.modelValue = v;
	}
);
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
}
.panel {
	margin-bottom: 10px;
}
.align-center {
	align-items: center;
}
.name {
	margin-left: 8px;
	font-size: 14px;
	color: #666;
}

.spec {
	margin-left: 12px;
	font-size: 12px;
	color: #999;
}

.global-goods-table-container {
	height: 100%;
}
</style>
