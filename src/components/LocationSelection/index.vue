<template>
	<div>
		<el-input v-model="showText" readonly @click="box = true" :placeholder="`${$t('avue-map.index.046541-0')}`"></el-input>
		<el-dialog width="100%" append-to-body modal-append-to-body :title="title" @close="handleClose" v-model="box">
			<div v-if="box">
				<el-input
					v-model="text"
					id="map__input"
					style="width: 50%; margin-bottom: 10px"
					:readonly="disabled"
					clearable
					:placeholder="`${$t('avue-map.index.046541-1')}`"
				></el-input>
				<div>
					<div id="searchMap" style="width: 100%; height: 400px"></div>
				</div>
			</div>
			<template v-slot:footer>
				<span v-if="!disabled">
					<div style="flex: 1; text-align: right">
						<el-button type="primary" @click="handleSubmit()">{{ $t('avue-map.index.046541-2') }}</el-button>
					</div>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<script>
export default {
	name: 'AvueMap',
	emits: ['update:modelValue'],
	props: {
		disabled: {
			type: Boolean,
			default: false,
		},
		modelValue: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			poi: {},
			marker: null,
			infoWindow: null,
			map: null,
			text: '',
			box: false,
		};
	},
	watch: {
		box: {
			handler() {
				if (this.box) {
					this.text = this.modelValue?.[2];
					this.$nextTick(() =>
						this.init(() => {
							if (this.longitude && this.latitude) {
								this.addMarker(this.longitude, this.latitude, this.text);
							}
						})
					);
				}
			},
			immediate: true,
		},
	},
	computed: {
		title() {
			return this.disabled ? this.$t('avue-map.index.046541-3') : this.$t('avue-map.index.046541-4');
		},
		longitude() {
			return this.modelValue?.[0] || 0;
		},
		latitude() {
			return this.modelValue?.[1] || 0;
		},
		showText() {
			return this.modelValue && this.modelValue[2];
		},
	},
	methods: {
		//新增坐标
		addMarker(longitude, latitude, address) {
			this.clearMarker();
			this.marker = new window.AMap.Marker({
				position: [longitude, latitude],
			});
			this.infoWindow = new AMap.InfoWindow({
				offset: new AMap.Pixel(0, -36),
			});
			this.marker.setMap(this.map);
			this.infoWindow.setMap(this.map);
			if (address) {
				this.marker.setPosition([longitude, latitude]);
				this.infoWindow.setPosition([longitude, latitude]);
				this.infoWindow.setContent('<div style="padding-top: 1em;">' + address + '</div>');
				this.infoWindow.open(this.map, this.marker.getPosition());
			}
			this.map.setCenter(this.marker.getPosition());
		},
		//清空坐标
		clearMarker() {
			if (this.marker) {
				this.marker.setMap(null);
				this.marker = null;
			}
			if (this.infoWindow) {
				this.infoWindow.setMap(null);
				this.infoWindow = null;
			}
		},
		//获取坐标
		getAddress(longitude, latitude) {
			this.map.plugin('AMap.Geocoder', () => {
				//回调函数
				let geocoder = new window.AMap.Geocoder({});
				geocoder.getAddress([longitude, latitude], (status, result) => {
					if (status === 'complete' && result.info === 'OK') {
						const regeocode = result.regeocode;
						this.poi = Object.assign(regeocode, {
							longitude,
							latitude,
						});
						this.text = regeocode.formattedAddress;
						this.poi.address = this.text;
						this.addMarker(longitude, latitude, this.text);
					}
				});
			});
		},
		handleClose() {
			this.text = '';
			this.poi = {};
			this.box = false;
			// window.poiPicker.clearSearchResults();
		},
		handleSubmit() {
			const { longitude, latitude, address, formattedAddress } = this.poi;
			const addr = address || formattedAddress;

			console.log(this.poi, 'longitude, latitude, addr');

			if (longitude && latitude && addr) {
				this.$emit('update:modelValue', [longitude, latitude, addr]);
			}
			this.poi = {};
			this.box = false;
		},
		addClick() {
			this.map.on('click', (e) => {
				const lnglat = e.lnglat;
				const longitude = lnglat.getLng();
				const latitude = lnglat.getLat();
				this.getAddress(longitude, latitude);
			});
		},
		init(callback) {
			this.map = new window.AMap.Map('searchMap', {
				zoom: 13,
				center: (() => {
					if (this.longitude && this.latitude) {
						return [this.longitude, this.latitude];
					}
				})(),
			});
			this.initPoip();
			this.addClick();
			callback();
		},
		initPoip() {
			window.AMapUI.loadUI(['misc/PoiPicker'], (PoiPicker) => {
				var poiPicker = new PoiPicker({
					input: 'map__input',
				});
				//初始化poiPicker
				this.poiPickerReady(poiPicker);
			});
		},
		poiPickerReady(poiPicker) {
			//选取了某个POI
			poiPicker.on('poiPicked', (poiResult) => {
				var poi = poiResult.item;
				const [longitude, latitude] = poi.location.toString().split(',');
				this.poi = Object.assign(poi, {
					formattedAddress: poi.name,
					longitude,
					latitude,
					address: poi.name,
				});
				this.addMarker(longitude, latitude, poi.name);
			});
		},
	},
};
</script>
