<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.jixiansc.com
  - 注意：
  - 本软件为www.jixiansc.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<div v-if="type == 'image'">
		<ul class="el-upload-list el-upload-list--picture-card" v-for="(item, index) in value" :key="index">
			<li tabindex="0" class="el-upload-list__item is-ready" :style="divStyle ? divStyle : 'width: ' + width + 'px;height: ' + height + 'px'">
				<div>
					<img :src="item" alt="" class="el-upload-list__item-thumbnail" />
					<span class="el-upload-list__item-actions">
						<span v-show="!disabled" class="el-upload-list__item-preview" v-if="index != 0" @click="moveMaterial(index, 'up')">
							<i class="el-icon-back"></i>
						</span>
						<span class="el-upload-list__item-preview" @click="zoomMaterial(index)">
							<i class="el-icon-view"></i>
						</span>
						<span v-show="!disabled" class="el-upload-list__item-delete" @click="deleteMaterial(index)">
							<i class="el-icon-delete"></i>
						</span>
						<span v-show="!disabled" class="el-upload-list__item-preview" v-if="index != value.length - 1" @click="moveMaterial(index, 'down')">
							<i class="el-icon-right"></i>
						</span>
					</span>
				</div>
			</li>
		</ul>
		<div
			tabindex="0"
			class="el-upload el-upload--picture-card"
			v-if="num > value.length"
			@click="toSeleteMaterial"
			:style="divStyle ? divStyle : 'width: ' + width + 'px;height: ' + height + 'px;' + 'line-height:' + height + 'px;'"
		>
			<el-icon style="margin-top: 15px"><plus /></el-icon>
			<!--      <i class="el-icon-plus"></i>-->
		</div>

		<el-dialog v-model="dialogVisible" append-to-body width="35%">
			<img :src="url" alt="" style="width: 100%" />
		</el-dialog>

		<el-dialog :title="t('material.list.232795-0')" v-model="listDialogVisible" append-to-body width="70%">
			<el-container>
				<el-aside width="unset">
					<div style="margin-bottom: 10px; margin-top: 20px">
						<el-button class="el-icon-plus" size="small" @click="materialgroupAddFn()"> {{ t('material.list.232795-1') }} </el-button>
					</div>
					<el-tabs style="height: 400px" tab-position="left" v-model="materialgroupObjId" v-loading="materialgroupLoading" @tab-click="tabClick">
						<el-tab-pane v-for="(item, index) in materialgroupList" :key="index" :name="item.id">
							<template #label> {{ item.name }}</template>
						</el-tab-pane>
					</el-tabs>
				</el-aside>
				<el-main>
					<el-card>
						<template #header>
							<el-row>
								<el-col :span="12">
									<span>{{ materialgroupObj.name }}</span>
									<span v-if="materialgroupObj.id != '-1'">
										<el-button
											size="small"
											type="text"
											class="el-icon-edit"
											style="margin-left: 10px"
											@click="materialgroupEditFn(materialgroupObj)"
											>{{ t('material.list.232795-2') }}</el-button
										>
										<el-button
											size="small"
											type="text"
											class="el-icon-delete"
											style="margin-left: 10px; color: red"
											@click="materialgroupDelete(materialgroupObj)"
											>{{ t('material.list.232795-3') }}</el-button
										>
									</span>
								</el-col>
								<el-col :span="12" style="text-align: right">
									<el-upload
										:action="uploadUrl"
										:headers="headers"
										:file-list="[]"
										:on-progress="handleProgress"
										:before-upload="beforeUpload"
										:on-success="handleSuccess"
										:on-error="handleError"
									>
										<el-button size="small" type="primary">{{ t('material.list.232795-4') }}</el-button>
									</el-upload>
								</el-col>
							</el-row>
						</template>
						<div v-loading="tableLoading">
							<el-alert v-if="tableData.length <= 0" :title="t('material.list.232795-5')" type="info" :closable="false" center show-icon> </el-alert>
							<el-row :gutter="5">
								<el-checkbox-group v-model="urls" :max="num - value.length">
									<el-col :span="4" v-for="(item, index) in tableData" :key="index">
										<el-card :body-style="{ padding: '5px' }">
											<el-image style="width: 100%; height: 200px" :src="item.url" fit="contain" :preview-src-list="[item.url]"></el-image>
											<div>
												<el-checkbox class="material-name" :label="item.url">{{ item.name }}</el-checkbox>
												<el-row class="compile">
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" @click="materialRename(item)">{{ t('material.list.232795-6') }}</el-button>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" @click="materialUrl(item)">{{ t('material.list.232795-7') }}</el-button>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-dropdown trigger="click" @command="handleCommand">
															<el-button type="text" class="button-do">{{ t('material.list.232795-8') }}<i class="el-icon-arrow-down"></i></el-button>
															<template #dropdown>
																<el-dropdown-menu>
																	<template v-for="(item2, idx) in materialgroupList">
																		<el-dropdown-item
																			v-if="idx > 0"
																			:key="idx"
																			:command="item.id + '-' + item2.id"
																			:disabled="item.groupId == item2.id"
																			>{{ item2.name }}</el-dropdown-item
																		>
																	</template>
																</el-dropdown-menu>
															</template>
														</el-dropdown>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" style="color: red" @click="materialDel(item)">{{
															t('material.list.232795-3')
														}}</el-button>
													</el-col>
												</el-row>
											</div>
										</el-card>
									</el-col>
								</el-checkbox-group>
							</el-row>
							<el-pagination
								v-model:current-page="page.currentPage"
								v-model:page-size="page.pageSize"
								@size-change="sizeChange"
								@current-change="currentChange"
								:page-sizes="[12, 24]"
								layout="total, sizes, prev, pager, next, jumper"
								:total="page.total"
								class="pagination"
								style="margin-top: 20px"
							>
							</el-pagination>
						</div>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="listDialogVisible = false">{{ t('material.list.232795-9') }}</el-button>
					<el-button type="primary" @click="sureUrls">{{ t('material.list.232795-10') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'yun-design';
import {
	getPage as materialgroupPage,
	addObj as materialgroupAdd,
	delObj as materialgroupDel,
	putObj as materialgroupEdit,
} from '@/api/mall/materialgroup';
import { getPage, addObj, delObj, putObj } from '@/api/mall/material';
import { useUserInfo } from '/@/stores/userInfo';
import { Session } from '/@/utils/storage';

const access_token = Session.getToken();

const { t } = useI18n();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const props = defineProps({
	shopId: {
		type: String,
	},
	value: {
		type: Array,
		default: () => [],
	},
	type: {
		type: String,
	},
	divStyle: {
		type: String,
	},
	num: {
		type: Number,
		default: 5,
	},
	width: {
		type: Number,
		default: 150,
	},
	height: {
		type: Number,
		default: 150,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['input', 'deleteMaterial', 'deleteMaterialM', 'sureSuccess']);

// 响应式数据
const uploadUrl = ref(`${import.meta.env.VITE_VUE_PROXY_URL}/admin/file/upload?fileType=image&dir=material/`);
const headers = ref({
	Authorization: 'Bearer ' + access_token,
	'tenant-id': userInfos.value.user.tenantId,
});
const dialogVisible = ref(false);
const url = ref('');
const listDialogVisible = ref(false);
const materialgroupList = ref([]);
const materialgroupObjId = ref('');
const materialgroupObj = ref({});
const materialgroupLoading = ref(false);
const tableData = ref([]);
const page = ref({
	total: 0,
	currentPage: 1,
	pageSize: 12,
	ascs: [],
	descs: 'create_time',
});
const tableLoading = ref(false);
const groupId = ref(null);
const urls = ref([]);

// 方法
const currentChange = (currentPage) => {
	page.value.currentPage = currentPage;
	getPageFn(page.value);
};

const moveMaterial = (index, type) => {
	if (type == 'up') {
		let tempOption = props.value[index - 1];
		props.value[index - 1] = props.value[index];
		props.value[index] = tempOption;
	}
	if (type == 'down') {
		let tempOption = props.value[index + 1];
		props.value[index + 1] = props.value[index];
		props.value[index] = tempOption;
	}
};

const zoomMaterial = (index) => {
	dialogVisible.value = true;
	url.value = props.value[index];
};

const deleteMaterial = async (index) => {
	try {
		await ElMessageBox.confirm(t('material.list.232795-11'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			type: 'warning',
		});

		const newValue = props.value.filter((value, indexIn) => indexIn !== index);
		emit('input', newValue);
		emit('deleteMaterial', urls.value);
		emit('deleteMaterialM', newValue);
	} catch (error) {
		// 用户取消操作
	}
};

const toSeleteMaterial = () => {
	listDialogVisible.value = true;
	if (tableData.value.length <= 0) {
		materialgroupPageFn();
	}
};

const materialgroupPageFn = async () => {
	materialgroupLoading.value = true;
	try {
		const response = await materialgroupPage({
			total: 0,
			current: 1,
			size: 999,
			ascs: [],
			descs: 'create_time',
			shopId: props.shopId,
		});

		let list = response.data.records;
		list.unshift({
			id: '-1',
			name: t('material.list.232795-15'),
		});
		materialgroupList.value = list;
		tabClick({
			index: 0,
		});
	} catch (error) {
		console.error(error);
	} finally {
		materialgroupLoading.value = false;
	}
};

const materialgroupDelete = async (obj) => {
	try {
		await ElMessageBox.confirm(t('material.list.232795-16'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			type: 'warning',
		});

		await materialgroupDel(obj.id);
		const index = materialgroupList.value.findIndex((item) => item.id === obj.id);
		if (index > -1) {
			materialgroupList.value.splice(index, 1);
		}
	} catch (error) {
		// 用户取消操作
	}
};

const materialgroupEditFn = async (obj) => {
	try {
		const { value } = await ElMessageBox.prompt(t('material.list.232795-17'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			inputValue: obj.name,
		});

		await materialgroupEdit({
			id: obj.id,
			name: value,
		});

		const index = materialgroupList.value.findIndex((item) => item.id === obj.id);
		if (index > -1) {
			materialgroupList.value[index].name = value;
		}
	} catch (error) {
		// 用户取消操作
	}
};

const materialgroupAddFn = async () => {
	try {
		const { value } = await ElMessageBox.prompt(t('material.list.232795-17'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			inputPattern: /[\S]/,
			inputErrorMessage: t('material.list.232795-18'),
		});

		await materialgroupAdd({
			shopId: props.shopId,
			name: value,
		});

		materialgroupPageFn();
	} catch (error) {
		// 用户取消操作
	}
};

const tabClick = (tab) => {
	urls.value = [];
	const index = Number(tab.index);
	const obj = materialgroupList.value[index];
	obj.index = index;
	materialgroupObj.value = obj;
	materialgroupObjId.value = obj.id;
	page.value.currentPage = 1;
	page.value.total = 0;
	groupId.value = obj.id !== '-1' ? obj.id : null;
	getPageFn(page.value);
};

const getPageFn = async (pageParams, params) => {
	tableLoading.value = true;
	try {
		const response = await getPage({
			current: pageParams.currentPage,
			size: pageParams.pageSize,
			descs: page.value.descs,
			ascs: page.value.ascs,
			groupId: groupId.value,
			shopId: props.shopId,
			...params,
		});

		tableData.value = response.data.records;
		page.value.total = response.data.total;
		page.value.currentPage = pageParams.currentPage;
		page.value.pageSize = pageParams.pageSize;
	} catch (error) {
		console.error(error);
	} finally {
		tableLoading.value = false;
	}
};

const sizeChange = (val) => {
	page.value.currentPage = 1;
	page.value.pageSize = val;
	getPageFn(page.value);
};

const materialRename = async (item) => {
	try {
		const { value } = await ElMessageBox.prompt(t('material.list.232795-19'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			inputValue: item.name,
		});

		await putObj({
			id: item.id,
			name: value,
		});

		getPageFn(page.value);
	} catch (error) {
		// 用户取消操作
	}
};

const materialUrl = async (item) => {
	try {
		await ElMessageBox.prompt(t('material.list.232795-20'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			inputValue: item.url,
		});
	} catch (error) {
		// 用户取消操作
	}
};

const materialDel = async (item) => {
	try {
		await ElMessageBox.confirm(t('material.list.232795-21'), t('material.list.232795-12'), {
			confirmButtonText: t('material.list.232795-13'),
			cancelButtonText: t('material.list.232795-14'),
			type: 'warning',
		});

		await delObj(item.id);
		const index = urls.value.indexOf(item.url);
		if (index !== -1) {
			urls.value.splice(index, 1);
		}
		getPageFn(page.value);
	} catch (error) {
		// 用户取消操作
	}
};

const handleCommand = async (command) => {
	const [id, groupId] = command.split('-');
	try {
		await putObj({
			id,
			groupId,
		});
		getPageFn(page.value);
	} catch (error) {
		console.error(error);
	}
};

const handleProgress = (event, file, fileList) => {
	// 可以在这里处理上传进度
};

const handleSuccess = async (response, file, fileList) => {
	try {
		await addObj({
			shopId: props.shopId,
			type: '1',
			groupId: groupId.value !== '-1' ? groupId.value : null,
			name: file.name,
			url: response.link,
		});
		getPageFn(page.value);
	} catch (error) {
		console.error(error);
	}
};

const handleError = (err) => {
	ElMessage.error(err + '');
};

const beforeUpload = (file) => {
	const isPic = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/jpg';
	const isLt1M = file.size / 1024 / 1024 < 1;

	if (!isPic) {
		ElMessage.error(t('material.list.232795-22'));
		return false;
	}

	if (!isLt1M) {
		ElMessage.error(t('material.list.232795-23'));
		return false;
	}

	return true;
};

const sureUrls = () => {
	urls.value.forEach((item) => {
		props.value.push(item);
	});
	emit('sureSuccess', urls.value);
	listDialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.material-name {
	padding: 0px 5px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	height: 20px;
	font-size: 13px;
	margin-top: 10px;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.compile {
	padding-top: 10px;
	padding-bottom: 10px;
	display: flex;
	align-items: center;
}

.col-do {
	text-align: center;
}

.button-do {
	padding: unset !important;
	font-size: 12px;
}
</style>
