<template>
	<el-dialog
		v-model="show"
		:width="width"
		:title="title"
		:show-close="true"
		:close-on-click-modal="true"
		:append-to-body="appendToBody"
		:close-on-press-escape="true"
		custom-class="import-dialog-wrapper"
		@close="close"
	>
		<div class="dialog-wrapper">
			<div class="button-wrapper">
				<el-button @click="downloadTemplate" v-show="!hideDownTemplate">{{ $t('import-dialog.index.065985-0') }}</el-button>
				<slot name="moreButton"></slot>
			</div>

			<el-upload
				class="upload-wrapper"
				drag
				name="fileRef"
				ref="fileRef"
				:auto-upload="false"
				:limit="1"
				:multiple="false"
				action="undefined"
				:file-list="fileList"
				:show-file-list="true"
				:on-exceed="handleExceed"
				:on-success="uploadSuccess"
				:on-change="onChange"
				:on-remove="onRemove"
				:on-error="uploadError"
				:disabled="loading"
			>
				<i class="el-icon-upload"></i>
				<div class="el-upload__text">
					{{ $t('import-dialog.index.065985-1') }}<em>{{ $t('import-dialog.index.065985-2') }}</em>
				</div>
				<div class="el-upload__tip">{{ $t('import-dialog.index.065985-3') }}</div>
				<template v-slot:tip>
					<div class="el-upload__tip error-info" v-if="errorUrl">
						<span class="error-msg">{{ $t('import-dialog.index.065985-4') }}</span>
						<a :href="errorUrl" :download="fileName">{{ $t('import-dialog.index.065985-5') }}</a>
					</div>
				</template>
			</el-upload>
			<div class="remark" v-html="remark"></div>
		</div>
		<template #footer>
			<el-button @click="close">{{ $t('import-dialog.index.065985-6') }}</el-button>
			<el-button type="primary" @click="submit" :loading="loading">{{ $t('import-dialog.index.065985-7') }}</el-button>
		</template>
	</el-dialog>
</template>

<script>
import { batchUploadExcel } from '@/api/common/upload';
const downloadUrl = `${import.meta.env.VITE_VUE_OSS_URL}/frontend/delivery-center/saas-fresh`;
export default {
	name: 'ImportDialog',
	props: {
		// 弹框宽度
		width: {
			type: String,
			default: '700px',
		},
		// 类型 order-订单
		type: {
			type: String,
			default: 'order',
		},
		// 是否展示
		visible: {
			type: Boolean,
			required: true,
			default: false,
		},
		appendToBody: {
			type: Boolean,
			default: false,
		},
		//是否有其他参数
		data: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			// 枚举映射
			enumObject: {
				order: {
					title: t('import-dialog.index.065985-8'),
					templateUrl: `${downloadUrl}/%E8%AE%A2%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-3.xlsx`,
					serverUrl: '/mall/orderInfoForCustome/saleOrderImport',
					remark: `
            批导说明：（*为必填项）<br>
              1、同一个客户，同一个配送方式，会合并生成一个销售单<br>
              2、日期的格式：yyyy-MM-dd<br>
              3、数量最多可保留3位小数，金额最多可保留8位小数<br>
              4、客户、商品编码都属于店铺下的信息，若匹配不上，当前行数据将不会导入<br>
              5、sku编码属于商品下，若匹配不上，当前行数据将不被导入<br>
              6、批量导入订单，支付方式统一为“账期支付”<br>
              7、收货地址取客户地址，若客户无地址需要客户管理中添加地址后才可以导入<br>
          `,
				},
				orderDispatch: {
					title: t('import-dialog.index.065985-17'),
					serverUrl: '/mall/orderinfo/batchDeliveryImport',
					hideDownTemplate: true,
					remark: `
            批导说明：（*为必填项）<br>
              1、请下载【发货订单】，并将物流单号填入<br>
              2、请勿删除修改导出的订单信息<br>
              3、建议一次最多导入100条<br>
          `,
				},
				shop: {
					title: t('import-dialog.index.065985-21'),
					templateUrl: `${downloadUrl}/%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-4.xlsx`,
					serverUrl: '/mall/goodsImport/import',
					remark: `
            批导说明：（*为必填项）<br>
              1、日期的格式：yyyy-MM-dd<br>
              2、数量最多可保留3位小数，金额最多可保留8位小数<br>
              3、图片，默认商城类目的二级图片。
          `,
				},
				purchase: {
					title: t('import-dialog.index.065985-25'),
					templateUrl: `${downloadUrl}/%E9%87%87%E8%B4%AD%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-3.xlsx`,
					serverUrl: '/wms/purchaseorder/importExcel',
					remark: `
            批导说明：（*为必填项）<br>
              1、同一个店铺、同一个供应商，同一个收货仓库，同一个预计入库日期，会合并生成一个采购单<br>
              2、日期的格式：yyyy-MM-dd<br>
              3、数量最多可保留3位小数，金额最多可保留8位小数<br>
              4、供应商、收货仓库、商品编码都属于店铺下的信息，若匹配不上，采购单导入失败<br>
              5、sku编码属于商品下，若匹配不上，采购单导入失败
          `,
				},
				supplier: {
					title: t('import-dialog.index.065985-29'),
					templateUrl: `${downloadUrl}/%E4%BE%9B%E5%BA%94%E5%95%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-3.xlsx`,
					serverUrl: '/wms/supplier/importExcel',
				},
				customer: {
					title: t('import-dialog.index.065985-30'),
					templateUrl: `${downloadUrl}/%E5%AE%A2%E6%88%B7%E4%BF%A1%E6%81%AF%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-2.xlsx`,
					serverUrl: '/mall/shop_customer_info/customerImport',
				},
				user: {
					title: t('import-dialog.index.065985-31'),
					templateUrl: `${downloadUrl}/%E7%94%A8%E6%88%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`,
					serverUrl: '/mall/userinfo/userImport',
				},
				goods: {
					title: t('import-dialog.index.065985-21'),
					templateUrl: `${downloadUrl}%E4%BE%9B%E5%BA%94%E5%95%86%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-4.xlsx`,
					serverUrl: '/mall/supplier_goods/supplierGoodsImport',
				},
				agreementGoods: {
					title: t('import-dialog.index.065985-21'),
					templateUrl: `${downloadUrl}/%E5%8D%8F%E8%AE%AE%E4%BB%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`,
					serverUrl: '/mall/shopAgreementInfo/agreementGoodsImport',
				},
				inGoods: {
					title: t('import-dialog.index.065985-21'),
					templateUrl: `${downloadUrl}/%E5%88%9B%E5%BB%BA%E9%87%87%E8%B4%AD%E5%8D%95%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`,
					serverUrl: '/wms/purchaseorder/supplierGoodsImport',
				},
				points: {
					title: t('import-dialog.index.065985-32'),
					templateUrl: `${downloadUrl}/%E5%AE%9E%E4%BD%93%E5%8D%A1%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`,
					serverUrl: '/mall/pointsCard/import',
				},
			},
			fileList: [],
			loading: false,
			errorUrl: '',
			fileName: '',
			show: false,
		};
	},
	mounted() {},
	computed: {
		// 标题
		title() {
			const { type } = this;
			return this.enumObject[type]['title'] || t('import-dialog.index.065985-32');
		},
		// 补充信息
		remark() {
			const { type } = this;
			return this.enumObject[type]['remark'] || '';
		},
		// 接口地址
		serverUrl() {
			const { type } = this;
			return this.enumObject[type]['serverUrl'] || '';
		},
		// 判断上传列表是否有数据
		existFile() {
			return this.fileList?.length === 0 ? false : true;
		},
		// 状态
		enableStatus() {
			return this.existFile && this.serverUrl;
		},
		// 隐藏下载模版
		hideDownTemplate() {
			const { type } = this;
			return this.enumObject[type]['hideDownTemplate'] || false;
		},
	},
	methods: {
		// 下载模板
		downloadTemplate() {
			const url = this.enumObject[this.type]['templateUrl'] || '';
			if (!url) return this.$message.error(t('import-dialog.index.065985-33'));
			url && window.open(url);
		},
		//关闭事件
		close() {
			this.$emit('update:visible', false);
			this.$emit('close');
			this.reset();
		},
		// 重置状态
		reset() {
			this.$refs.fileRef?.clearFiles();
			// this.$refs.fileRef?.submit()
			this.fileList = [];
			this.resetStatus();
		},
		// 自定义上传 - 确定按钮时间
		async submit() {
			if (!this.existFile) return this.$message.error(t('import-dialog.index.065985-34'));
			if (!this.enableStatus) return;
			this.loading = true;
			try {
				const formData = new FormData();
				formData.append('file', this.fileList[0]?.raw);
				if (this.data) {
					for (let key in this.data) {
						formData.append(key, this.data[key]);
					}
				}

				const res = await batchUploadExcel(this.serverUrl, formData);
				this.resetStatus();
				const type = res?.type;
				if (type === 'excel') {
					this.handleExcel(res);
					this.$refs.fileRef?.clearFiles();
					this.fileList = [];
				}
				if (type === 'json') {
					this.handleJson(res);
				}
			} catch (error) {
				console.log(error);
			}
			this.loading = false;
		},
		// 初始化一些数据
		resetStatus() {
			this.errorUrl = '';
			this.fileName = '';
		},
		// 处理excel
		handleExcel(res) {
			const { data, name } = res;
			this.errorUrl = data;
			this.fileName = name;
		},
		// 处理json
		handleJson(res) {
			const { msg, code } = res;
			if (code === 0) {
				this.$emit('update:visible', false);
				this.$emit('submit');
				this.$emit('load-success', res);
			} else {
				this.$refs.fileRef?.clearFiles();
				this.fileList = [];
			}
			this.$message.info(msg || t('import-dialog.index.065985-35'));
		},
		// 上传超出限制
		handleExceed() {
			this.$message.error(t('import-dialog.index.065985-36'));
		},
		// 文件变化
		onChange(file, fileList) {
			const { name } = file;
			const extension = name.slice(name.lastIndexOf('.') + 1);
			if (!['xls', 'xlsx'].includes(String(extension).toLocaleLowerCase())) {
				this.$message.error(t('import-dialog.index.065985-37'));
				this.fileList = [];
			} else {
				this.errorUrl = '';
				this.fileList = fileList;
			}
		},
		//上传成功
		uploadSuccess(response, file, fileList) {},
		// 上传失败
		uploadError(err, file, fileList) {},
		// 移除文件
		onRemove(file, fileList) {
			this.reset();
		},
	},
	watch: {
		visible(val) {
			this.show = val;
		},
	},
};
</script>

<style lang="scss" scoped>
::v-deep {
	.el-dialog__body {
		padding: 10px 20px;
	}
	.el-upload {
		width: 100%;
	}
	.el-upload-dragger {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
}

.button-wrapper {
	margin-bottom: 20px;
}

.error-msg {
	color: red;
	margin-right: 10px;
}

.remark {
	color: orangered;
	margin-top: 16px;
}
.error-info {
	font-size: 14px;
}
</style>
