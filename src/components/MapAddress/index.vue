<template>
	<div class="avue-map">
		<el-input v-model="showText" readonly @click="box = true" :placeholder="`${$t('MapAddress.index.474211-0')}`"></el-input>
		<yun-drawer v-model="box" :title="title" size="X-large" @close="handleClose">
			<div class="avue-map__content" v-if="box">
				<el-input
					class="avue-map__content-input"
					id="map__input"
					size="small"
					:readonly="disabled"
					v-model="text"
					clearable
					:placeholder="`${$t('MapAddress.index.474211-1')}`"
				></el-input>
				<div class="avue-map__content-box">
					<div id="map__container" class="avue-map__content-container" tabindex="0"></div>
					<div id="map__result" class="avue-map__content-result"></div>
				</div>
			</div>
			<template #footer class="dialog-footer" v-if="!disabled">
				<el-button type="primary" @click="handleSubmit">{{ $t('MapAddress.index.474211-2') }}</el-button>
			</template>
		</yun-drawer>
	</div>
</template>
<script>
import { ElMessage } from 'yun-design';

export default {
	name: 'MapAddress',
	props: {
		disabled: {
			type: Boolean,
			default: false,
		},
		value: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	emits: ['update:data'],
	data() {
		return {
			poi: {},
			marker: null,
			map: null,
			text: '',
			box: false,
		};
	},
	watch: {
		box: {
			handler() {
				if (this.box) {
					this.$nextTick(() =>
						this.init(() => {
							const { location } = this.value;
							const { lng, lat } = location || {};
							if (lng && lat) {
								this.addMarker(lng, lat);
								// this.getAddress(lng, lat);
							}
						})
					);
				}
			},
			immediate: true,
		},
	},
	computed: {
		title() {
			return this.disabled ? t('MapAddress.index.474211-3') : t('MapAddress.index.474211-4');
		},
		showText() {
			return this.value.address;
		},
	},
	methods: {
		poiPickerReady(poiPicker) {
			window.poiPicker = poiPicker;
			//选取了某个POI
			poiPicker.on('poiPicked', (poiResult) => {
				this.clearMarker();
				const { source, item: poi } = poiResult;
				const {
					entr_location,
					location: { lng, lat },
					address,
					name,
					...rest
				} = poi;
				this.poi = {
					...rest,
					address: name,
					location: {
						lng,
						lat,
					},
				};
				this.text = name;
				if (source !== 'search') {
					if (poi.adcode) {
						poiPicker.setCity(poi.adcode);
					}
					setTimeout(() => {
						poiPicker.searchByKeyword(poi.name);
					});
				}
			});
		},
		initPoip() {
			window.AMapUI.loadUI(['misc/PoiPicker'], (PoiPicker) => {
				var poiPicker = new PoiPicker({
					input: 'map__input',
					placeSearchOptions: {
						map: this.map,
						pageSize: 10,
					},
					searchResultsContainer: 'map__result',
				});
				//初始化poiPicker
				this.poiPickerReady(poiPicker);
			});
		},
		init(callback) {
			const { address, location } = this.value;
			this.poi = this.value;
			this.text = address;
			const { lng, lat } = location || {};
			const center = lng && lat ? [lng, lat] : null;
			this.map = new window.AMap.Map('map__container', {
				zoom: 13,
				center,
			});
			this.initPoip();
			this.addClick();
			callback();
		},
		//清空坐标
		clearMarker() {
			if (this.marker) {
				this.marker.setMap(null);
				this.marker = null;
			}
		},
		//新增坐标
		addMarker(R, P) {
			this.clearMarker();
			this.marker = new window.AMap.Marker({
				position: [R, P],
			});
			this.marker.setMap(this.map);
		},
		//获取坐标
		getAddress(R, P) {
			this.map.plugin('AMap.Geocoder', () => {
				//回调函数
				let geocoder = new window.AMap.Geocoder({});
				geocoder.getAddress([R, P], (status, result) => {
					if (status === 'complete' && result.info === 'OK') {
						const { regeocode } = result;
						const { addressComponent, formattedAddress, latitude, longitude } = regeocode;
						const { province: pname, city: cityname, district: adname, ...rest } = addressComponent;
						this.poi = {
							...rest,
							pname,
							cityname,
							adname,
							address: formattedAddress,
							location: {
								lng: R,
								lat: P,
							},
						};
						this.text = formattedAddress;
						if (latitude && longitude) {
							this.poi.location = {
								lng: latitude,
								lat: longitude,
							};
						}
						// 自定义点标记内容
						var markerContent = document.createElement('div');

						// 点标记中的图标
						var markerImg = document.createElement('img');
						markerImg.src = '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png';
						markerContent.appendChild(markerImg);

						// 点标记中的文本
						var markerSpan = document.createElement('span');
						markerSpan.className = 'avue-map__marker';
						markerSpan.innerHTML = result.regeocode.formattedAddress;
						markerContent.appendChild(markerSpan);
						this.marker.setContent(markerContent); //更新点标记内容
					}
				});
			});
		},
		addClick() {
			this.map.on('click', (e) => {
				if (this.disabled) return;
				const lnglat = e.lnglat;
				const R = lnglat.getLng();
				const P = lnglat.getLat();
				this.addMarker(R, P);
				this.getAddress(R, P);
			});
		},
		handleClose() {
			this.text = '';
			window.poiPicker.clearSearchResults();
			this.poi = {};
		},
		handleSubmit() {
			const { address, location } = this.poi;
			const { lng, lat } = location;
			if (!address || !lng || !lat) {
				ElMessage.info(t('MapAddress.index.474211-0'));
				return;
			}
			this.$emit('update:data', this.poi);
			this.poi = {};
			this.box = false;
		},
	},
};
</script>

<style lang="scss">
.amap-icon img,
.amap-marker img {
	width: 25px;
	height: 34px;
}
.avue-map {
	&__submit {
		width: 100%;
	}
	&__marker {
		position: absolute;
		top: -32px;
		right: -118px;
		color: #fff;
		padding: 4px 10px;
		box-shadow: 1px 1px 1px rgba(10, 10, 10, 0.2);
		white-space: nowrap;
		font-size: 12px;
		font-family: '';
		background-color: #25a5f7;
		border-radius: 3px;
	}
	&__content {
		&-input {
			margin-bottom: 10px;
		}
		&-box {
			position: relative;
		}
		&-container {
			width: 100%;
			height: 450px;
		}
		&-result {
			display: block !important;
			position: absolute;
			top: 0;
			right: -8px;
			width: 250px;
			height: 450px;
			overflow-y: auto;
		}
	}
}
</style>
