<template>
	<div class="shop-pages">
		<div class="flex flex-wrap link-list">
			<div
				class="link-item border border-br px-5 py-[5px] rounded-[3px] cursor-pointer mr-[10px] mb-[10px]"
				v-for="(item, index) in linkList"
				:class="{
					'border-primary text-primary': modelValue.path == item.path && modelValue.name == item.name,
				}"
				:key="index"
				@click="handleSelect(item)"
			>
				{{ item.name }}
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import { LinkTypeEnum, type Link } from '.';

defineProps({
	modelValue: {
		type: Object as PropType<Link>,
		default: () => ({}),
	},
});
const emit = defineEmits<{
	(event: 'update:modelValue', value: Link): void;
}>();

const linkList = ref([
	{
		path: '/pages/index/index',
		name: t('Link.shop-pages.308458-0'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/news/news',
		name: t('Link.shop-pages.308458-1'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/user/user',
		name: t('Link.shop-pages.308458-2'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/collection/collection',
		name: t('Link.shop-pages.308458-3'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/customer_service/customer_service',
		name: t('Link.shop-pages.308458-4'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/user_set/user_set',
		name: t('Link.shop-pages.308458-5'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/as_us/as_us',
		name: t('Link.shop-pages.308458-6'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/user_data/user_data',
		name: t('Link.shop-pages.308458-7'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/agreement/agreement',
		name: t('Link.shop-pages.308458-8'),
		query: {
			type: 'privacy',
		},
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/agreement/agreement',
		name: t('Link.shop-pages.308458-9'),
		query: {
			type: 'service',
		},
		type: LinkTypeEnum.SHOP_PAGES,
	},
	{
		path: '/pages/search/search',
		name: t('Link.shop-pages.308458-10'),
		type: LinkTypeEnum.SHOP_PAGES,
	},
]);

const handleSelect = (value: Link) => {
	emit('update:modelValue', value);
};
</script>
