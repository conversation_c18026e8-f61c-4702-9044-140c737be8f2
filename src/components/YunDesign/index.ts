/*
 * @Author: chenlei2
 * @Date: 2024-03-26 10:11:31
 * @LastEditors: chenlei2
 * @LastEditTime: 2024-04-07 15:44:29
 * @Description:
 */
import table from '@ylz-material/table/lib/index.es.js';
import '@ylz-material/table/lib/style.css';
import tableV2 from '@ylz-material/table-v2/lib/index.es.js';
import '@ylz-material/table-v2/lib/style.css';
import headerMenu from '@ylz-material/header-menu/lib/index.es.js';
import '@ylz-material/header-menu/lib/style.css';
// import sideMenu from '@ylz-material/side-menu'
// import '@ylz-material/side-menu/lib/style.css'
import pageTabs from '@ylz-material/page-tabs/lib/index.es.js';
import '@ylz-material/page-tabs/lib/style.css';
import rest from '@ylz-material/rest/lib/index.es.js';
import '@ylz-material/rest/lib/style.css';
import filter from '@ylz-material/filter/lib/index.es.js';
import '@ylz-material/filter/lib/style.css';
import drawer from '@ylz-material/drawer/lib/index.es.js';
import '@ylz-material/drawer/lib/style.css';
import dialog from '@ylz-material/dialog/lib/index.es.js';
import '@ylz-material/dialog/lib/style.css';
import batchOperation from '@ylz-material/batch-operation/lib/index.es.js';
import '@ylz-material/batch-operation/lib/style.css';
import descriptions from '@ylz-material/descriptions/lib/index.es.js';
import '@ylz-material/descriptions/lib/style.css';
import yunImport from '@/materials/import/index.js';
import yunUpload from '@ylz-material/upload';
import '@ylz-material/upload/lib/style.css';
import yunTask from '@ylz-material/task/lib/index.es.js';
import '@ylz-material/task/lib/style.css';
import yunExcelEdit from '@ylz-material/excel-edit/lib/index.es.js';
import '@ylz-material/excel-edit/lib/style.css';
import ProForm from '@ylz-material/pro-form/lib/index.es.js';
import '@ylz-material/pro-form/lib/style.css';
import ProTable from '@ylz-material/pro-table/lib/index.es.js';
import '@ylz-material/pro-table/lib/style.css';
import ProDetail from '@ylz-material/pro-detail/lib/index.es.js';
import '@ylz-material/pro-detail/lib/style.css';
import Pagination from '@ylz-material/pagination/lib/index.es.js';
import '@ylz-material/pagination/lib/style.css';
import ProSelect from '@ylz-material/pro-select/lib/index.es.js';
import '@ylz-material/pro-select/lib/style.css';

const components = [
	table,
	tableV2,
	headerMenu,
	// sideMenu,
	pageTabs,
	rest,
	filter,
	drawer,
	dialog,
	batchOperation,
	descriptions,
	yunImport,
	yunUpload,
	yunTask,
	yunExcelEdit,
	ProForm,
	ProTable,
	ProDetail,
	Pagination,
	ProSelect,
];

const install = (app) => {
	components.forEach((item) => {
		app.use(item);
	});
};

export default {
	install,
};
