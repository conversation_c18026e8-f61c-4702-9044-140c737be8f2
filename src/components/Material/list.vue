<template>
	<div v-if="type == 'image'">
		<ul class="el-upload-list el-upload-list--picture-card" v-for="(item, index) in renderValue" :key="index">
			<li tabindex="0" class="el-upload-list__item is-ready" :style="divStyle ? divStyle : 'width: ' + width + 'px;height: ' + height + 'px'">
				<div>
					<img :src="item" alt="" class="el-upload-list__item-thumbnail" />
					<span class="el-upload-list__item-actions">
						<span v-show="!disabled" class="el-upload-list__item-preview" v-if="index != 0" @click="moveMaterial(index, 'up')">
							<el-icon><arrow-left /></el-icon>
						</span>
						<span class="el-upload-list__item-preview" @click="zoomMaterial(index)">
							<el-icon><zoom-in /></el-icon>
						</span>
						<span v-show="!disabled" class="el-upload-list__item-delete" @click="deleteMaterial(index)">
							<el-icon><delete /></el-icon>
						</span>
						<span v-show="!disabled" class="el-upload-list__item-preview" v-if="index != renderValue.length - 1" @click="moveMaterial(index, 'down')">
							<el-icon><arrow-right /></el-icon>
						</span>
					</span>
				</div>
			</li>
		</ul>
		<div
			tabindex="0"
			class="el-upload el-upload--picture-card"
			v-if="num > renderValue.length"
			@click="toSeleteMaterial"
			:style="divStyle ? divStyle : 'width: ' + width + 'px;height: ' + height + 'px;' + 'line-height:' + height + 'px;'"
		>
			<div style="height: 100%; display: flex; justify-content: center; align-items: center"><plus /></div>
		</div>

		<el-dialog append-to-body v-model="dialogVisible" width="35%">
			<img :src="url" alt="" style="width: 100%" />
		</el-dialog>

		<el-drawer :title="$t('Material.list.812862-0')" :append-to-body="true" v-model="listDialogVisible" size="80%">
			<el-container>
				<el-aside width="unset">
					<div style="margin: 20px 0 10px 20px">
						<el-button :icon="Plus" size="small" @click="materialgroupAdd()"> {{ $t('Material.list.812862-1') }} </el-button>
					</div>
					<el-tabs style="height: 400px" tab-position="left" v-model="materialgroupObjId" v-loading="materialgroupLoading" @tab-click="tabClick">
						<el-tab-pane v-for="(item, index) in materialgroupList" :key="index" :label="item.name" :name="item.id"> </el-tab-pane>
					</el-tabs>
				</el-aside>
				<el-main>
					<el-card>
						<template #header>
							<el-row>
								<el-col :span="12">
									<span>{{ materialgroupObj.name }}</span>
									<span v-if="materialgroupObj.id != '-1'">
										<el-button size="small" type="text" class="el-icon-edit" style="margin-left: 10px" @click="materialgroupEdit(materialgroupObj)">{{
											$t('Material.list.812862-2')
										}}</el-button>
										<el-button
											size="small"
											type="text"
											class="el-icon-delete"
											style="margin-left: 10px; color: red"
											@click="materialgroupDelete(materialgroupObj)"
											>{{ $t('Material.list.812862-3') }}</el-button
										>
									</span>
								</el-col>
								<el-col :span="12" style="text-align: right">
									<el-upload
										ref="uploadRef"
										:action="uploadUrl"
										:headers="headers"
										:file-list="[]"
										:on-progress="handleProgress"
										:before-upload="beforeUpload"
										:on-success="handleSuccess"
										:on-error="handleError"
									>
										<el-button size="small" type="primary">{{ $t('Material.list.812862-4') }}</el-button>
									</el-upload>
								</el-col>
							</el-row>
						</template>
						<div v-loading="tableLoading">
							<el-alert v-if="tableData.length <= 0" :title="$t('Material.list.812862-5')" type="info" :closable="false" center show-icon> </el-alert>
							<el-row :gutter="5">
								<el-checkbox-group v-model="urls" :max="num - renderValue.length">
									<el-col :span="4" v-for="(item, index) in tableData" :key="index">
										<el-card :body-style="{ padding: '5px' }">
											<el-image style="width: 100%; height: 200px" :src="item.url" fit="contain" :preview-src-list="[item.url]"></el-image>
											<div>
												<el-checkbox class="material-name" :label="item.url">{{ item.name }}</el-checkbox>
												<el-row class="compile">
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" @click="materialRename(item)">{{ $t('Material.list.812862-6') }}</el-button>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" @click="materialUrl(item)">{{ $t('Material.list.812862-7') }}</el-button>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-dropdown trigger="click" @command="handleCommand">
															<el-button type="text" class="button-do"
																>{{ $t('Material.list.812862-8') }}<i class="el-icon-arrow-down"></i
															></el-button>
															<template #dropdown>
																<el-dropdown-menu>
																	<template v-for="(item2, idx) in materialgroupList">
																		<el-dropdown-item
																			v-if="idx > 0"
																			:key="idx"
																			:command="item.id + '-' + item2.id"
																			:disabled="item.groupId == item2.id"
																			>{{ item2.name }}</el-dropdown-item
																		>
																	</template>
																</el-dropdown-menu>
															</template>
														</el-dropdown>
													</el-col>
													<el-col :span="6" class="col-do">
														<el-button type="text" class="button-do" style="color: red" @click="materialDel(item)">{{
															$t('Material.list.812862-3')
														}}</el-button>
													</el-col>
												</el-row>
											</div>
										</el-card>
									</el-col>
								</el-checkbox-group>
							</el-row>
							<el-pagination
								@size-change="sizeChange"
								@current-change="currentChange"
								v-model:current-page="page.currentPage"
								:page-sizes="[12, 24]"
								:page-size="page.pageSize"
								layout="total, sizes, prev, pager, next, jumper"
								:total="page.total"
								class="pagination"
								style="margin-top: 20px"
							>
							</el-pagination>
						</div>
					</el-card>
				</el-main>
			</el-container>
			<template v-slot:footer>
				<span class="dialog-footer">
					<el-button @click="listDialogVisible = false">{{ $t('Material.list.812862-9') }}</el-button>
					<el-button type="primary" @click="sureUrls">{{ $t('Material.list.812862-10') }}</el-button>
				</span>
			</template>
		</el-drawer>
	</div>
</template>

<script setup>
import {
	getPage as fetchMaterialgroupPage,
	addObj as fetchMaterialgroupAdd,
	delObj as fetchMaterialgroupDel,
	putObj as fetchMaterialgroupEdit,
} from '@/api/mall/materialgroup';
import { ElMessage, ElMessageBox } from 'yun-design';
import { Plus } from '@yun-design/icons-vue';
import { ref, watch } from 'vue';
import { getPage as fetchGetPage, addObj, delObj, putObj } from '@/api/mall/material';
import { useUserInfo } from '/@/stores/userInfo';
import { Session } from '/@/utils/storage';
import { storeToRefs } from 'pinia';
const stores = useUserInfo();
const { userInfos, shopInfo } = storeToRefs(stores);
const tenantId = userInfos?.value?.tenantInfo?.id;
const access_token = Session.getToken();
const shopId = shopInfo.value.id;

const props = defineProps({
	//素材数据
	value: {
		type: Array,
		default() {
			return [];
		},
	},
	modelValue: {
		type: Array,
		default() {
			return [];
		},
	},
	//素材类型
	type: {
		type: String,
	},
	//自定义图片style
	divStyle: {
		type: String,
	},
	num: {
		type: Number,
		default() {
			return 5;
		},
	},
	//宽度
	width: {
		type: Number,
		default() {
			return 150;
		},
	},
	//宽度
	height: {
		type: Number,
		default() {
			return 150;
		},
	},
	disabled: {
		//是否禁用
		type: Boolean,
		default() {
			return false;
		},
	},
});
const $emit = defineEmits(['update:modelValue', 'sureSuccess', 'deleteMaterial', 'deleteMaterialM']);
const uploadUrl = ref(`${import.meta.env.VITE_VUE_PROXY_URL}/admin/file/upload?fileType=image&dir=material/`);
const headers = ref({
	Authorization: 'Bearer ' + access_token,
	'tenant-id': tenantId,
});
const dialogVisible = ref(false);
const url = ref('');
const listDialogVisible = ref(false);
const materialgroupList = ref([]);
const materialgroupObjId = ref('');
const materialgroupObj = ref({});
const materialgroupLoading = ref(false);
const tableData = ref([]);
const renderValue = ref(props.modelValue);
const page = ref({
	total: 0, // 总页数
	currentPage: 1, // 当前页数
	pageSize: 12, // 每页显示多少条
	ascs: [], //升序字段
	descs: 'create_time', //降序字段
});
const tableLoading = ref(false);
const groupId = ref(null);
const urls = ref([]);

const currentChange = (currentPage) => {
	page.value.currentPage = currentPage;
	getPage(page.value);
};

const moveMaterial = (index, type) => {
	if (type == 'up') {
		let tempOption = renderValue.value[index - 1];
		renderValue.value[index - 1] = renderValue.value[index];
		renderValue.value[index] = tempOption;
	}

	if (type == 'down') {
		let tempOption = renderValue.value[index + 1];
		renderValue.value[index + 1] = renderValue.value[index];
		renderValue.value[index] = tempOption;
	}
};

const zoomMaterial = (index) => {
	dialogVisible.value = true;
	url.value = renderValue.value[index];
};

const deleteMaterial = (index) => {
	ElMessageBox.confirm(t('Material.list.812862-11'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		type: 'warning',
	}).then(function () {
		const url = renderValue.value[index];
		let value = renderValue.value.filter((value, indexIn) => indexIn !== index);
		urls.value = urls.value.filter((v) => v !== url);
		$emit('update:modelValue', value);
		$emit('deleteMaterial', urls.value); //点击确认后的回调
		$emit('deleteMaterialM', value);
	});
};
const toSeleteMaterial = () => {
	listDialogVisible.value = true;
	if (tableData.value.length <= 0) {
		materialgroupPage();
	}
};

const materialgroupPage = () => {
	materialgroupLoading.value = true;
	fetchMaterialgroupPage({
		total: 0, // 总页数
		current: 1, // 当前页数
		size: 999, // 每页显示多少条
		ascs: [], //升序字段
		descs: 'create_time', //降序字段
		shopId,
	}).then((response) => {
		materialgroupLoading.value = false;
		let result = response.data.records;
		result.unshift({
			id: '-1',
			name: t('Material.list.812862-15'),
		});
		materialgroupList.value = result;
		tabClick({
			index: 0,
		});
	});
};

const materialgroupDelete = (materialgroupObj) => {
	ElMessageBox.confirm(t('Material.list.812862-16'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		type: 'warning',
	}).then(function () {
		fetchMaterialgroupDel(materialgroupObj.id).then(function () {
			materialgroupList.value = materialgroupList.value.filter((_, ind) => ind !== materialgroupObj.index);
		});
	});
};

const materialgroupEdit = (Obj) => {
	ElMessageBox.prompt(t('Material.list.812862-17'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		inputValue: Obj.name,
	})
		.then(({ value }) => {
			fetchMaterialgroupEdit({
				id: Obj.id,
				name: value,
			}).then(function () {
				Obj.name = value;
				materialgroupList.value[Obj.index].name = value;
			});
		})
		.catch(() => {});
};

const materialgroupAdd = () => {
	ElMessageBox.prompt(t('Material.list.812862-17'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		inputPattern: /[\S]/,
		inputErrorMessage: t('Material.list.812862-18'),
	})
		.then(({ value }) => {
			fetchMaterialgroupAdd({
				shopId,
				name: value,
			}).then(function () {
				materialgroupPage();
			});
		})
		.catch(() => {});
};
const tabClick = (tab, event) => {
	urls.value = [];
	let index = Number(tab.index);
	let materialgroupObjTem = materialgroupList.value[index];
	materialgroupObj.value = materialgroupObjTem;
	materialgroupObj.value.index = index;
	materialgroupObjId.value = materialgroupObj.value.id;
	page.value.currentPage = 1;
	page.value.total = 0;
	if (materialgroupObj.value.id != '-1') {
		groupId.value = materialgroupObj.value.id;
	} else {
		groupId.value = null;
	}
	getPage(page.value);
};
const getPage = (pageInfo, params) => {
	tableLoading.value = true;
	fetchGetPage(
		Object.assign(
			{
				current: pageInfo.currentPage,
				size: pageInfo.pageSize,
				descs: page.value.descs,
				ascs: page.value.ascs,
			},
			{
				groupId: groupId.value,
				shopId,
			}
		)
	)
		.then((response) => {
			page.value.total = response.data.total;
			page.value.currentPage = pageInfo.currentPage;
			page.value.pageSize = pageInfo.pageSize;
			tableData.value = response.data.records;
			tableLoading.value = false;
		})
		.catch(() => {
			tableLoading.value = false;
		});
};

const sizeChange = (val) => {
	page.value.currentPage = 1;
	page.value.pageSize = val;
	getPage(page.value);
};

const materialRename = (item) => {
	ElMessageBox.prompt(t('Material.list.812862-19'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		inputValue: item.name,
	})
		.then(({ value }) => {
			putObj({
				id: item.id,
				name: value,
			}).then(function () {
				getPage(page.value);
			});
		})
		.catch(() => {});
};
const materialUrl = (item) => {
	ElMessageBox.prompt(t('Material.list.812862-20'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		inputValue: item.url,
	})
		.then(({ value }) => {})
		.catch(() => {});
};
const materialDel = (item) => {
	ElMessageBox.confirm(t('Material.list.812862-21'), t('Material.list.812862-12'), {
		confirmButtonText: t('Material.list.812862-13'),
		cancelButtonText: t('Material.list.812862-14'),
		type: 'warning',
	}).then(function () {
		delObj(item.id).then(function () {
			let index = urls.value?.indexOf(item.url);
			if (index != -1) {
				urls.value.splice(index, 1);
			}
			getPage(page.value);
		});
	});
};

const handleCommand = (command) => {
	let s = command.split('-');
	putObj({
		id: s[0],
		groupId: s[1],
	}).then(function () {
		getPage(page.value);
	});
};

const handleProgress = (event, file, fileList) => {};

const uploadRef = ref(null);
const handleSuccess = (response, file, fileList) => {
	// uploadProgress.value = 0;
	addObj({
		shopId,
		type: '1',
		groupId: groupId.value != '-1' ? groupId.value : null,
		name: file.name,
		url: response.link,
	}).then(function () {
		uploadRef.value.clearFiles();
		getPage(page.value);
	});
};

const handleError = (err, file, fileList) => {
	ElMessage.error(err + '');
};

const beforeUpload = (file) => {
	const isPic = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/jpg';
	const isLt1M = file.size / 1024 / 1024 < 1;
	if (!isPic) {
		ElMessage.error(t('Material.list.812862-22'));
		return false;
	}
	if (!isLt1M) {
		ElMessage.error(t('Material.list.812862-23'));
	}
	return isPic && isLt1M;
};

const sureUrls = () => {
	urls.value.forEach((item) => {
		renderValue.value[renderValue.value.length] = item;
	});
	$emit('update:modelValue', urls.value);
	$emit('sureSuccess', urls.value); //点击确认后的回调
	listDialogVisible.value = false;
};
// 监听 value 和 modelValue 的变化
watch(
	[() => props.value, () => props.modelValue],
	([newValue, newModelValue]) => {
		if (newValue && newValue.length > 0) {
			renderValue.value = newValue;
		} else if (newModelValue && newModelValue.length > 0) {
			renderValue.value = newModelValue;
		} else {
			renderValue.value = [];
		}
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.material-name {
	padding: 0px 5px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	height: 20px;
	font-size: 13px;
	margin-top: 10px;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.compile {
	padding-top: 10px;
	padding-bottom: 10px;
}

.col-do {
	text-align: center;
}

.button-do {
	padding: unset !important;
	font-size: 12px;
}

.el-checkbox-group {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}
</style>
