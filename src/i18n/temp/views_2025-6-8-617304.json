{"zh": {"hooks.useLocalData.172602-0": "单据日期", "hooks.useLocalData.172602-1": "开始时间", "hooks.useLocalData.172602-2": "结束时间", "hooks.useLocalData.172602-3": "完成时间", "hooks.useLocalData.172602-4": "单据类型", "hooks.useLocalData.172602-5": "请选择单据类型", "hooks.useLocalData.172602-6": "商品", "hooks.useLocalData.172602-7": "请输入商品", "hooks.useLocalData.172602-8": "开票状态", "hooks.useLocalData.172602-9": "请选择开票状态", "hooks.useLocalData.172602-10": "支付状态", "hooks.useLocalData.172602-11": "请选择支付状态", "hooks.useLocalData.172602-12": "序号", "hooks.useLocalData.172602-13": "单号", "hooks.useLocalData.172602-14": "供应商名称", "hooks.useLocalData.172602-15": "供应商编码", "hooks.useLocalData.172602-16": "创建时间", "hooks.useLocalData.172602-17": "创建人", "hooks.useLocalData.172602-18": "批次", "hooks.useLocalData.172602-19": "到货日期", "hooks.useLocalData.172602-20": "配送仓库", "hooks.useLocalData.172602-21": "入库时间", "hooks.useLocalData.172602-22": "商品编码", "hooks.useLocalData.172602-23": "商品分类", "hooks.useLocalData.172602-24": "采购单位", "hooks.useLocalData.172602-25": "含税单价", "hooks.useLocalData.172602-26": "元", "hooks.useLocalData.172602-27": "税率%", "hooks.useLocalData.172602-28": "不含税单价", "hooks.useLocalData.172602-29": "数量", "hooks.useLocalData.172602-30": "不含税总价", "hooks.useLocalData.172602-31": "采购总价", "hooks.useLocalData.172602-32": "税金", "hooks.useLocalData.172602-33": "抵扣税率%", "hooks.useLocalData.172602-34": "抵扣税额", "hooks.useLocalData.172602-35": "抵扣后成本单价", "hooks.useLocalData.172602-36": "抵扣后成本金额"}, "en": {"hooks.useLocalData.172602-0": "Document date", "hooks.useLocalData.172602-1": "Start Time", "hooks.useLocalData.172602-2": "End Time", "hooks.useLocalData.172602-3": "Completion time", "hooks.useLocalData.172602-4": "Document type", "hooks.useLocalData.172602-5": "Please select the document type", "hooks.useLocalData.172602-6": "Product name", "hooks.useLocalData.172602-7": "Please enter the product name", "hooks.useLocalData.172602-8": "Invoice status", "hooks.useLocalData.172602-9": "Please select the invoicing status", "hooks.useLocalData.172602-10": "payment status", "hooks.useLocalData.172602-11": "Please select payment status", "hooks.useLocalData.172602-12": "Serial number", "hooks.useLocalData.172602-13": "odd numbers", "hooks.useLocalData.172602-14": "Supplier Name", "hooks.useLocalData.172602-15": "Supplier code", "hooks.useLocalData.172602-16": "Creation time", "hooks.useLocalData.172602-17": "founder", "hooks.useLocalData.172602-18": "batch", "hooks.useLocalData.172602-19": "arrival date", "hooks.useLocalData.172602-20": "Distributing Warehouse", "hooks.useLocalData.172602-21": "Storage time", "hooks.useLocalData.172602-22": "Product code", "hooks.useLocalData.172602-23": "Product classification", "hooks.useLocalData.172602-24": "Purchasing unit", "hooks.useLocalData.172602-25": "Unit price including tax", "hooks.useLocalData.172602-26": "element", "hooks.useLocalData.172602-27": "Tax rate%", "hooks.useLocalData.172602-28": "Unit price excluding tax", "hooks.useLocalData.172602-29": "quantity", "hooks.useLocalData.172602-30": "Total price excluding tax", "hooks.useLocalData.172602-31": "Total purchase price", "hooks.useLocalData.172602-32": "taxes", "hooks.useLocalData.172602-33": "Deduction tax rate%", "hooks.useLocalData.172602-34": "Tax credit ", "hooks.useLocalData.172602-35": "Unit price of cost after deduction", "hooks.useLocalData.172602-36": "Cost amount after deduction"}}