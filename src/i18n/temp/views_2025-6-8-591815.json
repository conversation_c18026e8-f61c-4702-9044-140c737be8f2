{"zh": {"hooks.useLocalData.916468-0": "日期段", "hooks.useLocalData.916468-1": "开始时间", "hooks.useLocalData.916468-2": "结束时间", "hooks.useLocalData.916468-3": "成品名称", "hooks.useLocalData.916468-4": "请输入成品名称", "hooks.useLocalData.916468-5": "原料名称", "hooks.useLocalData.916468-6": "请输入原料名称", "hooks.useLocalData.916468-7": "任务单号", "hooks.useLocalData.916468-8": "加工日期", "hooks.useLocalData.916468-9": "经办人", "hooks.useLocalData.916468-10": "成品一级分类", "hooks.useLocalData.916468-11": "成品二级分类", "hooks.useLocalData.916468-12": "成品编码", "hooks.useLocalData.916468-13": "成品规格", "hooks.useLocalData.916468-14": "成品数量", "hooks.useLocalData.916468-15": "原料一级分类", "hooks.useLocalData.916468-16": "原料二级分类", "hooks.useLocalData.916468-17": "原料编码", "hooks.useLocalData.916468-18": "原料规格", "hooks.useLocalData.916468-19": "原料消耗数量", "hooks.useLocalData.916468-20": "损耗量", "hooks.useLocalData.916468-21": "损耗率"}, "en": {"hooks.useLocalData.916468-0": "Date range", "hooks.useLocalData.916468-1": "Start Time", "hooks.useLocalData.916468-2": "End Time", "hooks.useLocalData.916468-3": "Product Name", "hooks.useLocalData.916468-4": "Please enter the name of the finished product", "hooks.useLocalData.916468-5": "Raw material name", "hooks.useLocalData.916468-6": "Please enter the name of the raw material", "hooks.useLocalData.916468-7": "Task Order Number", "hooks.useLocalData.916468-8": "Processing date", "hooks.useLocalData.916468-9": "Handled by", "hooks.useLocalData.916468-10": "First level classification of finished products", "hooks.useLocalData.916468-11": "Secondary classification of finished products", "hooks.useLocalData.916468-12": "Finished product code", "hooks.useLocalData.916468-13": "Finished product specifications", "hooks.useLocalData.916468-14": "Quantity of finished products", "hooks.useLocalData.916468-15": "Primary classification of raw materials", "hooks.useLocalData.916468-16": "Secondary classification of raw materials", "hooks.useLocalData.916468-17": "Raw material code", "hooks.useLocalData.916468-18": "Raw material specifications", "hooks.useLocalData.916468-19": "Quantity of raw material consumption", "hooks.useLocalData.916468-20": "wastage", "hooks.useLocalData.916468-21": "Loss rate"}}