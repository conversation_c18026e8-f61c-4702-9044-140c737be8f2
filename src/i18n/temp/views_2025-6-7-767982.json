{"zh": {"orderProductLeger.tableColumns.679280-0": "日期", "orderProductLeger.tableColumns.679280-1": "商品编码", "orderProductLeger.tableColumns.679280-2": "商品", "orderProductLeger.tableColumns.679280-3": "商品图片", "orderProductLeger.tableColumns.679280-4": "期初库存", "orderProductLeger.tableColumns.679280-5": "期初金额", "orderProductLeger.tableColumns.679280-6": "采购入库数量", "orderProductLeger.tableColumns.679280-7": "采购入库金额", "orderProductLeger.tableColumns.679280-8": "其他入库数量", "orderProductLeger.tableColumns.679280-9": "其他入库金额", "orderProductLeger.tableColumns.679280-10": "退货入库数量", "orderProductLeger.tableColumns.679280-11": "退货入库金额", "orderProductLeger.tableColumns.679280-12": "保溢单入库数量", "orderProductLeger.tableColumns.679280-13": "保溢单入库金额", "orderProductLeger.tableColumns.679280-14": "盘赢入库数量", "orderProductLeger.tableColumns.679280-15": "盘赢入库金额", "orderProductLeger.tableColumns.679280-16": "成品入库数量", "orderProductLeger.tableColumns.679280-17": "成品入库金额", "orderProductLeger.tableColumns.679280-18": "盘亏出库数量", "orderProductLeger.tableColumns.679280-19": "盘亏出库金额", "orderProductLeger.tableColumns.679280-20": "销售出库数量", "orderProductLeger.tableColumns.679280-21": "销售出库金额", "orderProductLeger.tableColumns.679280-22": "采购退货出库数量", "orderProductLeger.tableColumns.679280-23": "采购退货出库金额", "orderProductLeger.tableColumns.679280-24": "其它出库数量", "orderProductLeger.tableColumns.679280-25": "其它出库金额", "orderProductLeger.tableColumns.679280-26": "报损出库数量", "orderProductLeger.tableColumns.679280-27": "报损出库金额", "orderProductLeger.tableColumns.679280-28": "领料出库数量", "orderProductLeger.tableColumns.679280-29": "领料出库金额", "orderProductLeger.tableColumns.679280-30": "期末库存", "orderProductLeger.tableColumns.679280-31": "期末金额", "orderProductLeger.tableColumns.679280-32": "操作", "orderProductLeger.tableColumns.679280-33": "单据日期", "orderProductLeger.tableColumns.679280-34": "开始时间", "orderProductLeger.tableColumns.679280-35": "结束时间", "orderProductLeger.tableColumns.679280-36": "请输入选择", "orderProductLeger.tableColumns.679280-37": "仓库"}, "en": {"orderProductLeger.tableColumns.679280-0": "date", "orderProductLeger.tableColumns.679280-1": "product code", "orderProductLeger.tableColumns.679280-2": "Product name", "orderProductLeger.tableColumns.679280-3": "Product Image", "orderProductLeger.tableColumns.679280-4": "opening inventory", "orderProductLeger.tableColumns.679280-5": "Initial amount", "orderProductLeger.tableColumns.679280-6": "Purchase inventory quantity", "orderProductLeger.tableColumns.679280-7": "Purchase inventory amount", "orderProductLeger.tableColumns.679280-8": "Other inbound quantities", "orderProductLeger.tableColumns.679280-9": "Other inbound amounts", "orderProductLeger.tableColumns.679280-10": "Quantity of returned goods in storage", "orderProductLeger.tableColumns.679280-11": "Return inventory amount", "orderProductLeger.tableColumns.679280-12": "Inventory quantity of excess orders", "orderProductLeger.tableColumns.679280-13": "Excess inventory amount on the insurance policy", "orderProductLeger.tableColumns.679280-14": "Winning inventory quantity", "orderProductLeger.tableColumns.679280-15": "Winning inventory amount", "orderProductLeger.tableColumns.679280-16": "Finished product inventory quantity", "orderProductLeger.tableColumns.679280-17": "Finished product inventory amount", "orderProductLeger.tableColumns.679280-18": "Inventory loss and outbound quantity", "orderProductLeger.tableColumns.679280-19": "Inventory loss outflow amount", "orderProductLeger.tableColumns.679280-20": "Sales outbound quantity", "orderProductLeger.tableColumns.679280-21": "Sales outbound amount", "orderProductLeger.tableColumns.679280-22": "Purchase, return, and outbound quantity", "orderProductLeger.tableColumns.679280-23": "Purchase return and outbound amount", "orderProductLeger.tableColumns.679280-24": "Other outbound quantities", "orderProductLeger.tableColumns.679280-25": "Other outbound amounts", "orderProductLeger.tableColumns.679280-26": "Reported loss and outbound quantity", "orderProductLeger.tableColumns.679280-27": "Reported loss and outbound amount", "orderProductLeger.tableColumns.679280-28": "Material requisition and outbound quantity", "orderProductLeger.tableColumns.679280-29": "Material requisition and outbound amount", "orderProductLeger.tableColumns.679280-30": "End of period inventory", "orderProductLeger.tableColumns.679280-31": "Final amount", "orderProductLeger.tableColumns.679280-32": "operate", "orderProductLeger.tableColumns.679280-33": "Document date", "orderProductLeger.tableColumns.679280-34": "start time", "orderProductLeger.tableColumns.679280-35": "End Time", "orderProductLeger.tableColumns.679280-36": "Please enter your selection", "orderProductLeger.tableColumns.679280-37": "Warehouse"}}