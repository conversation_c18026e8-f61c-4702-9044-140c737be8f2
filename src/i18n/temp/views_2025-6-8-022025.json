{"zh": {"components.import.219577-0": "导入/导出", "components.import.219577-1": "确 认", "components.import.219577-2": "取 消", "components.import.219577-3": "1、导出盘点表时根据筛选条件导出;", "components.import.219577-4": "2、导入表格中盘点数量未填写，则表示此商品不进行盘点;", "components.import.219577-5": "3、导入成功后会自动生成盘点单，需要审核才能生效;", "components.import.219577-6": "4、导入失败不会生成盘点单，会提示失败原因;", "components.import.219577-7": "5、最多支持导入1000条记录;", "components.import.219577-8": "1、按筛选条件导出盘点表;", "components.import.219577-9": "2、导入盘点表"}, "en": {"components.import.219577-0": "Import/Export", "components.import.219577-1": "Confirm", "components.import.219577-2": "Cancel", "components.import.219577-3": "1. Export inventory table based on filtering criteria;", "components.import.219577-4": "2. If the inventory quantity is not filled in the import table, it means that this product will not be inventoried;", "components.import.219577-5": "3. After successful import, an inventory sheet will be automatically generated and needs to be reviewed before it can take effect;", "components.import.219577-6": "4. Import failure will not generate an inventory sheet and will prompt the reason for the failure;", "components.import.219577-7": "5. Supports importing up to 1000 records;", "components.import.219577-8": "1. Export inventory table based on filtering criteria;", "components.import.219577-9": "2. Import inventory sheet"}}