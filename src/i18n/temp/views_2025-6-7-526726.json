{"zh": {"hooks.useOptions.266569-0": "发物流", "hooks.useOptions.266569-1": "商家配送", "hooks.useOptions.266569-2": "虚拟发货", "hooks.useOptions.266569-3": "客户自提", "hooks.useOptions.266569-4": "待付款", "hooks.useOptions.266569-5": "待处理", "hooks.useOptions.266569-6": "待排线", "hooks.useOptions.266569-7": "待分拣", "hooks.useOptions.266569-8": "待发货", "hooks.useOptions.266569-9": "待收货", "hooks.useOptions.266569-10": "已收货", "hooks.useOptions.266569-11": "已完成", "hooks.useOptions.266569-12": "已取消", "hooks.useOptions.266569-13": "已回单", "hooks.useOptions.266569-14": "未回单", "hooks.useOptions.266569-15": "已付款", "hooks.useOptions.266569-16": "普通H5", "hooks.useOptions.266569-17": "小程序", "hooks.useOptions.266569-18": "后台新增", "hooks.useOptions.266569-19": "账期支付", "hooks.useOptions.266569-20": "微信支付", "hooks.useOptions.266569-21": "积分支付", "hooks.useOptions.266569-22": "普通快递", "hooks.useOptions.266569-23": "上门自提", "hooks.useOptions.266569-24": "未分拣", "hooks.useOptions.266569-25": "部分分拣", "hooks.useOptions.266569-26": "全部分拣", "hooks.useOptions.266569-27": "未发货", "hooks.useOptions.266569-28": "已发货", "hooks.useOptions.266569-29": "早配", "hooks.useOptions.266569-30": "明天下午", "hooks.useOptions.266569-31": "订单号", "hooks.useOptions.266569-32": "客户名称", "hooks.useOptions.266569-33": "到货日期", "hooks.useOptions.266569-34": "单据日期", "hooks.useOptions.266569-35": "客户编码", "hooks.useOptions.266569-36": "线路", "hooks.useOptions.266569-37": "收货地址", "hooks.useOptions.266569-38": "下单金额", "hooks.useOptions.266569-39": "发货金额", "hooks.useOptions.266569-40": "押金品金额", "hooks.useOptions.266569-41": "仓库", "hooks.useOptions.266569-42": "订单状态", "hooks.useOptions.266569-43": "支付方式", "hooks.useOptions.266569-44": "订单来源", "hooks.useOptions.266569-45": "订单备注", "hooks.useOptions.266569-46": "临时分拣码", "hooks.useOptions.266569-47": "下单人", "hooks.useOptions.266569-48": "下单时间", "hooks.useOptions.266569-49": "退货金额", "hooks.useOptions.266569-50": "司机", "hooks.useOptions.266569-51": "操作", "hooks.useOptions.266569-52": "配送日期", "hooks.useOptions.266569-53": "开始时间", "hooks.useOptions.266569-54": "结束时间", "hooks.useOptions.266569-55": "下单日期", "hooks.useOptions.266569-56": "签收日期"}, "en": {"hooks.useOptions.266569-0": "Send logistics", "hooks.useOptions.266569-1": "Merchant delivery", "hooks.useOptions.266569-2": "Virtual shipping", "hooks.useOptions.266569-3": "Customer self pickup", "hooks.useOptions.266569-4": "obligation", "hooks.useOptions.266569-5": "Pending processing", "hooks.useOptions.266569-6": "Waiting line", "hooks.useOptions.266569-7": "To be sorted", "hooks.useOptions.266569-8": "Pending shipment", "hooks.useOptions.266569-9": "Pending receipt of goods", "hooks.useOptions.266569-10": "Received goods", "hooks.useOptions.266569-11": "Completed", "hooks.useOptions.266569-12": "Cancelled", "hooks.useOptions.266569-13": "Received Order", "hooks.useOptions.266569-14": "Unreturned receipts", "hooks.useOptions.266569-15": "paid", "hooks.useOptions.266569-16": "Ordinary H5", "hooks.useOptions.266569-17": "small program", "hooks.useOptions.266569-18": "New backend addition", "hooks.useOptions.266569-19": "Payment terms", "hooks.useOptions.266569-20": "WeChat Pay", "hooks.useOptions.266569-21": "Integral payment", "hooks.useOptions.266569-22": "Ordinary express delivery", "hooks.useOptions.266569-23": "Door to door self pickup", "hooks.useOptions.266569-24": "Unclassified", "hooks.useOptions.266569-25": "Partial sorting", "hooks.useOptions.266569-26": "All sorting", "hooks.useOptions.266569-27": "Unshipped", "hooks.useOptions.266569-28": "Shipped", "hooks.useOptions.266569-29": "Early Match", "hooks.useOptions.266569-30": "tomorrow afternoon", "hooks.useOptions.266569-31": "Order No.", "hooks.useOptions.266569-32": "Customer Name", "hooks.useOptions.266569-33": "Delivery date", "hooks.useOptions.266569-34": "Document date", "hooks.useOptions.266569-35": "customer code", "hooks.useOptions.266569-36": "line", "hooks.useOptions.266569-37": "Receiving address", "hooks.useOptions.266569-38": "Order amount", "hooks.useOptions.266569-39": "Delivery amount", "hooks.useOptions.266569-40": "Deposit amount", "hooks.useOptions.266569-41": "warehouse", "hooks.useOptions.266569-42": "order status", "hooks.useOptions.266569-43": "Payment method", "hooks.useOptions.266569-44": "ORDER SOURCE", "hooks.useOptions.266569-45": "Order remarks", "hooks.useOptions.266569-46": "Temporary sorting code", "hooks.useOptions.266569-47": "Single player placement", "hooks.useOptions.266569-48": "Order time", "hooks.useOptions.266569-49": "Return amount", "hooks.useOptions.266569-50": "driver", "hooks.useOptions.266569-51": "operation", "hooks.useOptions.266569-52": "Delivery date", "hooks.useOptions.266569-53": "start time", "hooks.useOptions.266569-54": "End Time", "hooks.useOptions.266569-55": "Order date", "hooks.useOptions.266569-56": "Signing date"}}