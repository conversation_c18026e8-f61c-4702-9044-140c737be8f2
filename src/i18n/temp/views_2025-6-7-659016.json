{"zh": {"hooks.useLocalData.589708-0": "仓库", "hooks.useLocalData.589708-1": "请选择状态", "hooks.useLocalData.589708-2": "商品编码", "hooks.useLocalData.589708-3": "请输入商品编码", "hooks.useLocalData.589708-4": "商品", "hooks.useLocalData.589708-5": "请输入商品", "hooks.useLocalData.589708-6": "到货日期", "hooks.useLocalData.589708-7": "请选择到货日期", "hooks.useLocalData.589708-8": "规格", "hooks.useLocalData.589708-9": "单位", "hooks.useLocalData.589708-10": "采购类型", "hooks.useLocalData.589708-11": "销售方式", "hooks.useLocalData.589708-12": "批次", "hooks.useLocalData.589708-13": "实物库存数量", "hooks.useLocalData.589708-14": "可用库存数量", "hooks.useLocalData.589708-15": "销售数量", "hooks.useLocalData.589708-16": "待生产数量"}, "en": {"hooks.useLocalData.589708-0": "warehouse", "hooks.useLocalData.589708-1": "Please select the status", "hooks.useLocalData.589708-2": "product code", "hooks.useLocalData.589708-3": "Please enter the product code", "hooks.useLocalData.589708-4": "Product name", "hooks.useLocalData.589708-5": "Please enter the product name", "hooks.useLocalData.589708-6": "arrival date", "hooks.useLocalData.589708-7": "Please select the delivery date", "hooks.useLocalData.589708-8": "specifications", "hooks.useLocalData.589708-9": "unit", "hooks.useLocalData.589708-10": "procurement type", "hooks.useLocalData.589708-11": "Sales method", "hooks.useLocalData.589708-12": "batch", "hooks.useLocalData.589708-13": "Physical inventory quantity", "hooks.useLocalData.589708-14": "Available inventory quantity", "hooks.useLocalData.589708-15": "sales volumes", "hooks.useLocalData.589708-16": "Quantity to be produced"}}