{"zh": {"hooks.useLocalData.797621-0": "单据日期", "hooks.useLocalData.797621-1": "开始时间", "hooks.useLocalData.797621-2": "结束时间", "hooks.useLocalData.797621-3": "完成时间", "hooks.useLocalData.797621-4": "单据类型", "hooks.useLocalData.797621-5": "请选择单据类型", "hooks.useLocalData.797621-6": "商品", "hooks.useLocalData.797621-7": "请输入商品", "hooks.useLocalData.797621-8": "开票状态", "hooks.useLocalData.797621-9": "请选择开票状态", "hooks.useLocalData.797621-10": "支付状态", "hooks.useLocalData.797621-11": "请选择支付状态", "hooks.useLocalData.797621-12": "序号", "hooks.useLocalData.797621-13": "单号", "hooks.useLocalData.797621-14": "供应商名称", "hooks.useLocalData.797621-15": "供应商编码", "hooks.useLocalData.797621-16": "创建时间", "hooks.useLocalData.797621-17": "创建人", "hooks.useLocalData.797621-18": "批次", "hooks.useLocalData.797621-19": "到货日期", "hooks.useLocalData.797621-20": "配送仓库", "hooks.useLocalData.797621-21": "入库时间", "hooks.useLocalData.797621-22": "商品编码", "hooks.useLocalData.797621-23": "商品分类", "hooks.useLocalData.797621-24": "采购单位", "hooks.useLocalData.797621-25": "含税单价", "hooks.useLocalData.797621-26": "元", "hooks.useLocalData.797621-27": "税率%", "hooks.useLocalData.797621-28": "不含税单价", "hooks.useLocalData.797621-29": "数量", "hooks.useLocalData.797621-30": "不含税总价", "hooks.useLocalData.797621-31": "采购总价", "hooks.useLocalData.797621-32": "税金", "hooks.useLocalData.797621-33": "抵扣税率%", "hooks.useLocalData.797621-34": "抵扣税额", "hooks.useLocalData.797621-35": "抵扣后成本单价", "hooks.useLocalData.797621-36": "抵扣后成本金额"}, "en": {"hooks.useLocalData.797621-0": "Document date", "hooks.useLocalData.797621-1": "Start Time", "hooks.useLocalData.797621-2": "End time", "hooks.useLocalData.797621-3": "Completion time", "hooks.useLocalData.797621-4": "Document type", "hooks.useLocalData.797621-5": "Please select the document type", "hooks.useLocalData.797621-6": "Product Name", "hooks.useLocalData.797621-7": "Please enter the product name", "hooks.useLocalData.797621-8": "Invoice status", "hooks.useLocalData.797621-9": "Please select the invoicing status", "hooks.useLocalData.797621-10": "payment status", "hooks.useLocalData.797621-11": "Please select payment status", "hooks.useLocalData.797621-12": "Serial Number", "hooks.useLocalData.797621-13": "odd numbers", "hooks.useLocalData.797621-14": "Supplier Name", "hooks.useLocalData.797621-15": "Supplier code", "hooks.useLocalData.797621-16": "Creation time", "hooks.useLocalData.797621-17": "founder", "hooks.useLocalData.797621-18": "batch", "hooks.useLocalData.797621-19": "arrival date", "hooks.useLocalData.797621-20": "Distributing Warehouse", "hooks.useLocalData.797621-21": "Storage time", "hooks.useLocalData.797621-22": "product code", "hooks.useLocalData.797621-23": "Product classification", "hooks.useLocalData.797621-24": "Purchasing unit", "hooks.useLocalData.797621-25": "Unit price including tax", "hooks.useLocalData.797621-26": "first", "hooks.useLocalData.797621-27": "Tax rate%", "hooks.useLocalData.797621-28": "Unit price excluding tax", "hooks.useLocalData.797621-29": "quantity", "hooks.useLocalData.797621-30": "Total price excluding tax", "hooks.useLocalData.797621-31": "Total purchase price", "hooks.useLocalData.797621-32": "Taxes", "hooks.useLocalData.797621-33": "Deduction tax rate%", "hooks.useLocalData.797621-34": "Tax credit ", "hooks.useLocalData.797621-35": "Unit price of cost after deduction", "hooks.useLocalData.797621-36": "Cost amount after deduction"}}