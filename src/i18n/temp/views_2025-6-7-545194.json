{"zh": {"pointsconfig.form.451570-0": "积分初始设置", "pointsconfig.form.451570-1": "初始积分", "pointsconfig.form.451570-2": "请输入", "pointsconfig.form.451570-3": "请输入初始积分", "pointsconfig.form.451570-4": "清零时间", "pointsconfig.form.451570-5": "每年", "pointsconfig.form.451570-6": "永不清零", "pointsconfig.form.451570-7": "请选择清零时间", "pointsconfig.form.451570-8": "请选择", "pointsconfig.form.451570-9": "积分新增规则", "pointsconfig.form.451570-10": "订单支付生成积分", "pointsconfig.form.451570-11": "积分", "pointsconfig.form.451570-12": "说明：仅保留整数，向下取整。如订单金额为128.9元，转换为1.00=1.0积分，则转换积分为128分", "pointsconfig.form.451570-13": "实体卡（充值卡）兑换生成积分", "pointsconfig.form.451570-14": "请输入实体卡（充值卡）兑换比例", "pointsconfig.form.451570-15": "说明：仅保留整数，向下取整。如订单金额为3000元，转换为1.00=1.0积分，则转换积分为3000分", "pointsconfig.form.451570-16": "商品兑换生成积分", "pointsconfig.form.451570-17": "说明：设置后，该商品支付成功后，自动按兑换规则生成积分，如100积分商品，付款成功后则生成100积分", "pointsconfig.form.451570-18": "积分扣减规则", "pointsconfig.form.451570-19": "订单支付减扣积分", "pointsconfig.form.451570-20": "积分 = 1.00", "pointsconfig.form.451570-21": "请输入积分扣减规则", "pointsconfig.form.451570-22": "说明：仅保留整数，向上取整。如实体卡为128.9元，转换为1.00积分=1元，则扣除积分为129分", "pointsconfig.form.451570-23": "积分规则更新成功", "pointsconfig.form.451570-24": "提交", "pointsconfig.form.451570-25": "清空"}, "en": {"pointsconfig.form.451570-0": "Initial setting of points", "pointsconfig.form.451570-1": "Initial integral", "pointsconfig.form.451570-2": "Enter", "pointsconfig.form.451570-3": "Please enter the initial points", "pointsconfig.form.451570-4": "Reset time", "pointsconfig.form.451570-5": "every year", "pointsconfig.form.451570-6": "Never reset to zero", "pointsconfig.form.451570-7": "Please select the reset time", "pointsconfig.form.451570-8": "Please select", "pointsconfig.form.451570-9": "Rules for adding points", "pointsconfig.form.451570-10": "Order payment generates points", "pointsconfig.form.451570-11": "points", "pointsconfig.form.451570-12": "Explanation: Keep only integers, rounded down. If the order amount is 128.9 yuan and converted to 1.00=1.0 points, the converted points will be 128 points", "pointsconfig.form.451570-13": "Redemption of physical cards (recharge cards) to generate points", "pointsconfig.form.451570-14": "Please enter the exchange rate for physical cards (recharge cards)", "pointsconfig.form.451570-15": "Explanation: Keep only integers, rounded down. If the order amount is 3000 yuan and converted to 1.00=1.0 points, the converted points will be 3000 points", "pointsconfig.form.451570-16": "Redemption of goods generates points", "pointsconfig.form.451570-17": "Explanation: After setting, the product will automatically generate points according to the redemption rules after successful payment. For example, for a 100 point product, 100 points will be generated after successful payment", "pointsconfig.form.451570-18": "Points deduction rules", "pointsconfig.form.451570-19": "Order payment deduction points", "pointsconfig.form.451570-20": "Points=1.00", "pointsconfig.form.451570-21": "Please enter the deduction rules for points", "pointsconfig.form.451570-22": "Explanation: Keep only integers, rounded up. If the physical card costs 128.9 yuan and is converted to 1.00 points=1 yuan, the deducted points will be 129 points", "pointsconfig.form.451570-23": "Points rule updated successfully", "pointsconfig.form.451570-24": "submit", "pointsconfig.form.451570-25": "clear"}}