{"zh": {"hooks.useLocalData.926460-0": "单据日期", "hooks.useLocalData.926460-1": "开始时间", "hooks.useLocalData.926460-2": "结束时间", "hooks.useLocalData.926460-3": "完成时间", "hooks.useLocalData.926460-4": "单号", "hooks.useLocalData.926460-5": "请输入单号", "hooks.useLocalData.926460-6": "单据类型", "hooks.useLocalData.926460-7": "请选择单据类型", "hooks.useLocalData.926460-8": "客户名称", "hooks.useLocalData.926460-9": "请输入客户名称", "hooks.useLocalData.926460-10": "商品", "hooks.useLocalData.926460-11": "请输入商品", "hooks.useLocalData.926460-12": "开票状态", "hooks.useLocalData.926460-13": "请选择开票状态", "hooks.useLocalData.926460-14": "支付状态", "hooks.useLocalData.926460-15": "请选择支付状态", "hooks.useLocalData.926460-16": "序号", "hooks.useLocalData.926460-17": "客户分类", "hooks.useLocalData.926460-18": "收货人", "hooks.useLocalData.926460-19": "联系电话", "hooks.useLocalData.926460-20": "收货地址", "hooks.useLocalData.926460-21": "送货日期", "hooks.useLocalData.926460-22": "送货时段", "hooks.useLocalData.926460-23": "仓库", "hooks.useLocalData.926460-24": "订单来源", "hooks.useLocalData.926460-25": "线路", "hooks.useLocalData.926460-26": "支付方式", "hooks.useLocalData.926460-27": "司机", "hooks.useLocalData.926460-28": "创建人", "hooks.useLocalData.926460-29": "创建时间", "hooks.useLocalData.926460-30": "商品编码", "hooks.useLocalData.926460-31": "商品分类", "hooks.useLocalData.926460-32": "销售单位", "hooks.useLocalData.926460-33": "含税单价", "hooks.useLocalData.926460-34": "元", "hooks.useLocalData.926460-35": "税率", "hooks.useLocalData.926460-36": "不含税单价", "hooks.useLocalData.926460-37": "数量", "hooks.useLocalData.926460-38": "不含税总价", "hooks.useLocalData.926460-39": "税额", "hooks.useLocalData.926460-40": "含税总价", "hooks.useLocalData.926460-41": "成本总价", "hooks.useLocalData.926460-42": "毛利", "hooks.useLocalData.926460-43": "毛利率"}, "en": {"hooks.useLocalData.926460-0": "Document date", "hooks.useLocalData.926460-1": "Start Time", "hooks.useLocalData.926460-2": "End time", "hooks.useLocalData.926460-3": "Completion time", "hooks.useLocalData.926460-4": "odd numbers", "hooks.useLocalData.926460-5": "Please enter the tracking number", "hooks.useLocalData.926460-6": "Document type", "hooks.useLocalData.926460-7": "Please select the document type", "hooks.useLocalData.926460-8": "Customer Name", "hooks.useLocalData.926460-9": "Please enter the customer name", "hooks.useLocalData.926460-10": "Product Name", "hooks.useLocalData.926460-11": "Please enter the product name", "hooks.useLocalData.926460-12": "Invoice status", "hooks.useLocalData.926460-13": "Please select the invoicing status", "hooks.useLocalData.926460-14": "payment status", "hooks.useLocalData.926460-15": "Please select payment status", "hooks.useLocalData.926460-16": "Serial number", "hooks.useLocalData.926460-17": "customer classification", "hooks.useLocalData.926460-18": "consignee", "hooks.useLocalData.926460-19": "Contact Number", "hooks.useLocalData.926460-20": "Receiving address", "hooks.useLocalData.926460-21": "DELIVERY DATE", "hooks.useLocalData.926460-22": "Delivery Time", "hooks.useLocalData.926460-23": "warehouse", "hooks.useLocalData.926460-24": "ORDER SOURCE", "hooks.useLocalData.926460-25": "line", "hooks.useLocalData.926460-26": "Payment method", "hooks.useLocalData.926460-27": "driver", "hooks.useLocalData.926460-28": "Creator", "hooks.useLocalData.926460-29": "Creation time", "hooks.useLocalData.926460-30": "product code", "hooks.useLocalData.926460-31": "CATEGORY", "hooks.useLocalData.926460-32": "Sales Unit", "hooks.useLocalData.926460-33": "Unit price including tax", "hooks.useLocalData.926460-34": "element", "hooks.useLocalData.926460-35": "tax rate", "hooks.useLocalData.926460-36": "Unit price excluding tax", "hooks.useLocalData.926460-37": "quantity", "hooks.useLocalData.926460-38": "Total price excluding tax", "hooks.useLocalData.926460-39": "Tax amount", "hooks.useLocalData.926460-40": "Total price including tax", "hooks.useLocalData.926460-41": "Total cost", "hooks.useLocalData.926460-42": "Gross profit", "hooks.useLocalData.926460-43": "gross margin"}}