{"zh": {"pointsconfig.form.099175-0": "积分初始设置", "pointsconfig.form.099175-1": "初始积分", "pointsconfig.form.099175-2": "请输入", "pointsconfig.form.099175-3": "请输入初始积分", "pointsconfig.form.099175-4": "清零时间", "pointsconfig.form.099175-5": "每年", "pointsconfig.form.099175-6": "永不清零", "pointsconfig.form.099175-7": "请选择清零时间", "pointsconfig.form.099175-8": "请选择", "pointsconfig.form.099175-9": "积分新增规则", "pointsconfig.form.099175-10": "订单支付生成积分", "pointsconfig.form.099175-11": "积分", "pointsconfig.form.099175-12": "说明：仅保留整数，向下取整。如订单金额为128.9元，转换为1.00=1.0积分，则转换积分为128分", "pointsconfig.form.099175-13": "实体卡（充值卡）兑换生成积分", "pointsconfig.form.099175-14": "请输入实体卡（充值卡）兑换比例", "pointsconfig.form.099175-15": "说明：仅保留整数，向下取整。如订单金额为3000元，转换为1.00=1.0积分，则转换积分为3000分", "pointsconfig.form.099175-16": "商品兑换生成积分", "pointsconfig.form.099175-17": "说明：设置后，该商品支付成功后，自动按兑换规则生成积分，如100积分商品，付款成功后则生成100积分", "pointsconfig.form.099175-18": "积分扣减规则", "pointsconfig.form.099175-19": "订单支付减扣积分", "pointsconfig.form.099175-20": "积分 = 1.00", "pointsconfig.form.099175-21": "请输入积分扣减规则", "pointsconfig.form.099175-22": "说明：仅保留整数，向上取整。如实体卡为128.9元，转换为1.00积分=1元，则扣除积分为129分", "pointsconfig.form.099175-23": "积分规则更新成功", "pointsconfig.form.099175-24": "提交", "pointsconfig.form.099175-25": "清空"}, "en": {"pointsconfig.form.099175-0": "Initial setting of points", "pointsconfig.form.099175-1": "Initial integral", "pointsconfig.form.099175-2": "Enter", "pointsconfig.form.099175-3": "Please enter the initial points", "pointsconfig.form.099175-4": "Reset time", "pointsconfig.form.099175-5": "every year", "pointsconfig.form.099175-6": "Never reset to zero", "pointsconfig.form.099175-7": "Please select the reset time", "pointsconfig.form.099175-8": "Please select", "pointsconfig.form.099175-9": "Rules for adding points", "pointsconfig.form.099175-10": "Order payment generates points", "pointsconfig.form.099175-11": "points", "pointsconfig.form.099175-12": "Explanation: Keep only integers, rounded down. If the order amount is 128.9 yuan and converted to 1.00=1.0 points, the converted points will be 128 points", "pointsconfig.form.099175-13": "Redemption of physical cards (recharge cards) to generate points", "pointsconfig.form.099175-14": "Please enter the exchange rate for physical cards (recharge cards)", "pointsconfig.form.099175-15": "Explanation: Keep only integers, rounded down. If the order amount is 3000 yuan and converted to 1.00=1.0 points, the converted points will be 3000 points", "pointsconfig.form.099175-16": "Redemption of goods generates points", "pointsconfig.form.099175-17": "Explanation: After setting, the product will automatically generate points according to the redemption rules after successful payment. For example, for a 100 point product, 100 points will be generated after successful payment", "pointsconfig.form.099175-18": "Points deduction rules", "pointsconfig.form.099175-19": "Order payment deduction points", "pointsconfig.form.099175-20": "Points=1.00", "pointsconfig.form.099175-21": "Please enter the deduction rules for points", "pointsconfig.form.099175-22": "Explanation: Keep only integers, rounded up. If the physical card costs 128.9 yuan and is converted to 1.00 points=1 yuan, the deducted points will be 129 points", "pointsconfig.form.099175-23": "Points rule updated successfully", "pointsconfig.form.099175-24": "Submit", "pointsconfig.form.099175-25": "clear"}}