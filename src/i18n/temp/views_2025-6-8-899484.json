{"zh": {"wxapp.index.994391-0": "机构名称", "wxapp.index.994391-1": "输入关键字进行过滤", "wxapp.index.994391-2": "请输入协议标题", "wxapp.index.994391-3": "请输入协议内容", "wxapp.index.994391-4": "新增协议", "wxapp.index.994391-5": "新增", "wxapp.index.994391-6": "一键授权添加", "wxapp.index.994391-7": "点击上传", "wxapp.index.994391-8": "请上传apiclient_cert.p12", "wxapp.index.994391-9": "授权信息", "wxapp.index.994391-10": "查看access-token", "wxapp.index.994391-11": "api次数清零", "wxapp.index.994391-12": "服务器域名", "wxapp.index.994391-13": "用户隐私保护指引", "wxapp.index.994391-14": "创建时间：", "wxapp.index.994391-15": "重新授权", "wxapp.index.994391-16": "代码管理", "wxapp.index.994391-17": "查看", "wxapp.index.994391-18": "编辑", "wxapp.index.994391-19": "删除", "wxapp.index.994391-20": "查看权限集", "wxapp.index.994391-21": "消息管理权限", "wxapp.index.994391-22": "用户管理权限", "wxapp.index.994391-23": "帐号服务权限", "wxapp.index.994391-24": "网页服务权限", "wxapp.index.994391-25": "微信小店权限", "wxapp.index.994391-26": "微信多客服权限", "wxapp.index.994391-27": "群发与通知权限", "wxapp.index.994391-28": "微信卡券权限", "wxapp.index.994391-29": "微信扫一扫权限", "wxapp.index.994391-30": "微信连 WI-FI 权限", "wxapp.index.994391-31": "素材管理权限", "wxapp.index.994391-32": "微信摇周边权限", "wxapp.index.994391-33": "微信门店权限", "wxapp.index.994391-34": "自定义菜单权限", "wxapp.index.994391-35": "城市服务接口权限", "wxapp.index.994391-36": "微信电子发票权限", "wxapp.index.994391-37": "微信开放平台帐号管理权限", "wxapp.index.994391-38": "帐号管理权限", "wxapp.index.994391-39": "开发管理与数据分析权限", "wxapp.index.994391-40": "客服消息管理权限", "wxapp.index.994391-41": "小程序基本信息设置权限", "wxapp.index.994391-42": "小程序附近地点权限集", "wxapp.index.994391-43": "小程序插件管理权限集", "wxapp.index.994391-44": "版本管理", "wxapp.index.994391-45": "审核版本", "wxapp.index.994391-46": "审核ID", "wxapp.index.994391-47": "审核状态", "wxapp.index.994391-48": "审核成功", "wxapp.index.994391-49": "审核被拒绝", "wxapp.index.994391-50": "审核中", "wxapp.index.994391-51": "已撤回", "wxapp.index.994391-52": "拒绝原因", "wxapp.index.994391-53": "截图示例", "wxapp.index.994391-54": "截图", "wxapp.index.994391-55": "操作", "wxapp.index.994391-56": "撤回审核", "wxapp.index.994391-57": "发布版本", "wxapp.index.994391-58": "已发布", "wxapp.index.994391-59": "你暂无提交审核的版本或者版本已发布上线", "wxapp.index.994391-60": "已上传代码", "wxapp.index.994391-61": "所有页面", "wxapp.index.994391-62": "体验版二维码", "wxapp.index.994391-63": "提交审核", "wxapp.index.994391-64": "模板库", "wxapp.index.994391-65": "模板版本号", "wxapp.index.994391-66": "添加时间", "wxapp.index.994391-67": "模板描述", "wxapp.index.994391-68": "上传代码", "wxapp.index.994391-69": "设置服务器域名", "wxapp.index.994391-70": "请确保填写的域名已经在微信第三方平台配置", "wxapp.index.994391-71": "request合法域名", "wxapp.index.994391-72": "请输入request合法域名", "wxapp.index.994391-73": "socket合法域名", "wxapp.index.994391-74": "请输入socket合法域名", "wxapp.index.994391-75": "uploadFile合法域名", "wxapp.index.994391-76": "请输入uploadFile合法域名", "wxapp.index.994391-77": "downloadFile合法域名", "wxapp.index.994391-78": "请输入downloadFile合法域名", "wxapp.index.994391-79": "确认提交", "wxapp.index.994391-80": "提交成功", "wxapp.index.994391-81": "接入地址", "wxapp.index.994391-82": "是否确认要清零微信接口次数", "wxapp.index.994391-83": "提示", "wxapp.index.994391-84": "确定", "wxapp.index.994391-85": "取消", "wxapp.index.994391-86": "清零成功", "wxapp.index.994391-87": "小程序是否已经开通直播功能？", "wxapp.index.994391-88": "已经开通", "wxapp.index.994391-89": "未开通", "wxapp.index.994391-90": "小程序代码上传成功", "wxapp.index.994391-91": "是否确认提交审核？", "wxapp.index.994391-92": "提交审核成功", "wxapp.index.994391-93": "是否确认删除【{0}】", "wxapp.index.994391-94": "删除成功", "wxapp.index.994391-95": "修改成功", "wxapp.index.994391-96": "添加成功", "wxapp.index.994391-97": "设置成功", "wxapp.index.994391-98": "是否确认发布版本?", "wxapp.index.994391-99": "发布成功"}, "en": {"wxapp.index.994391-0": "Institution Name", "wxapp.index.994391-1": "Enter keywords for filtering", "wxapp.index.994391-2": "Please enter the agreement title", "wxapp.index.994391-3": "Please enter the content of the agreement", "wxapp.index.994391-4": "New agreement", "wxapp.index.994391-5": "newly added", "wxapp.index.994391-6": "One click authorization to add", "wxapp.index.994391-7": "Click to upload", "wxapp.index.994391-8": "Please upload apiclient _cert. p12", "wxapp.index.994391-9": "Authorization Information", "wxapp.index.994391-10": "View access token", "wxapp.index.994391-11": "API count reset to zero", "wxapp.index.994391-12": "Server Domain Name", "wxapp.index.994391-13": "User Privacy Protection Guidelines", "wxapp.index.994391-14": "Creation time:", "wxapp.index.994391-15": "Reauthorization", "wxapp.index.994391-16": "Code Management", "wxapp.index.994391-17": "view", "wxapp.index.994391-18": "edit", "wxapp.index.994391-19": "delete", "wxapp.index.994391-20": "View permission set", "wxapp.index.994391-21": "Message management permissions", "wxapp.index.994391-22": "User management permissions", "wxapp.index.994391-23": "Account service permissions", "wxapp.index.994391-24": "Web service permissions", "wxapp.index.994391-25": "WeChat store permissions", "wxapp.index.994391-26": "Multiple customer service permissions on WeChat", "wxapp.index.994391-27": "Group sending and notification permissions", "wxapp.index.994391-28": "WeChat coupon permissions", "wxapp.index.994391-29": "WeChat scan permission", "wxapp.index.994391-30": "WeChat Connect WI-FI Permissions", "wxapp.index.994391-31": "Material management permissions", "wxapp.index.994391-32": "WeChat Shake Peripheral Permissions", "wxapp.index.994391-33": "WeChat store permissions", "wxapp.index.994391-34": "Customize menu permissions", "wxapp.index.994391-35": "Urban service interface permissions", "wxapp.index.994391-36": "WeChat electronic invoice permission", "wxapp.index.994391-37": "WeChat Open Platform Account Management Permissions", "wxapp.index.994391-38": "Account management permissions", "wxapp.index.994391-39": "Development management and data analysis permissions", "wxapp.index.994391-40": "Customer service message management permission", "wxapp.index.994391-41": "Basic information setting permissions for mini programs", "wxapp.index.994391-42": "Location permission set near the mini program", "wxapp.index.994391-43": "Mini Program Plugin Management Permission Set", "wxapp.index.994391-44": "Versioning", "wxapp.index.994391-45": "Review version", "wxapp.index.994391-46": "Audit ID", "wxapp.index.994391-47": "Audit status", "wxapp.index.994391-48": "Audit successful", "wxapp.index.994391-49": "Review rejected", "wxapp.index.994391-50": "in review", "wxapp.index.994391-51": "Withdrawn", "wxapp.index.994391-52": "Reason for refusal", "wxapp.index.994391-53": "Screenshot example", "wxapp.index.994391-54": "screenshot", "wxapp.index.994391-55": "operate", "wxapp.index.994391-56": "Withdraw review", "wxapp.index.994391-57": "Release version", "wxapp.index.994391-58": "Published", "wxapp.index.994391-59": "You currently have no version to submit for review or the version has been released online", "wxapp.index.994391-60": "Code uploaded", "wxapp.index.994391-61": "All pages", "wxapp.index.994391-62": "Experience version QR code", "wxapp.index.994391-63": "Submit for review", "wxapp.index.994391-64": "Template Library", "wxapp.index.994391-65": "Template version number", "wxapp.index.994391-66": "Add time", "wxapp.index.994391-67": "Template Description", "wxapp.index.994391-68": "Upload code", "wxapp.index.994391-69": "Set server domain name", "wxapp.index.994391-70": "Please ensure that the domain name filled in has been configured on a third-party WeChat platform", "wxapp.index.994391-71": "Request a legitimate domain name", "wxapp.index.994391-72": "Please enter a valid domain name for the request", "wxapp.index.994391-73": "Socket legitimate domain name", "wxapp.index.994391-74": "Please enter a valid domain name for the socket", "wxapp.index.994391-75": "UploadFile legitimate domain name", "wxapp.index.994391-76": "Please enter a valid domain name for uploadFile", "wxapp.index.994391-77": "DownloadFile legitimate domain name", "wxapp.index.994391-78": "Please enter a valid domain name for downloadFile", "wxapp.index.994391-79": "Confirm submission", "wxapp.index.994391-80": "Submitted successfully", "wxapp.index.994391-81": "access address", "wxapp.index.994391-82": "Are you sure you want to reset the WeChat interface count to zero", "wxapp.index.994391-83": "Tips", "wxapp.index.994391-84": "sure", "wxapp.index.994391-85": "cancel", "wxapp.index.994391-86": "Zeroing successful", "wxapp.index.994391-87": "Has the mini program already enabled live streaming function?", "wxapp.index.994391-88": "Already activated", "wxapp.index.994391-89": "Not activated", "wxapp.index.994391-90": "Mini program code uploaded successfully", "wxapp.index.994391-91": "Are you sure to submit for review?", "wxapp.index.994391-92": "Successfully submitted for review", "wxapp.index.994391-93": "Are you sure to delete [{0}]", "wxapp.index.994391-94": "Delete successfully", "wxapp.index.994391-95": "Modified successfully", "wxapp.index.994391-96": "Added successfully", "wxapp.index.994391-97": "Setting successful", "wxapp.index.994391-98": "Are you sure to release the version?", "wxapp.index.994391-99": "Published successfully"}}