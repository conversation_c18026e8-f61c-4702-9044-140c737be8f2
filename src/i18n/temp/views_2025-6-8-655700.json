{"zh": {"hooks.useLocalData.556374-0": "单据日期", "hooks.useLocalData.556374-1": "开始时间", "hooks.useLocalData.556374-2": "结束时间", "hooks.useLocalData.556374-3": "完成时间", "hooks.useLocalData.556374-4": "单据类型", "hooks.useLocalData.556374-5": "请选择单据类型", "hooks.useLocalData.556374-6": "商品", "hooks.useLocalData.556374-7": "请输入商品", "hooks.useLocalData.556374-8": "开票状态", "hooks.useLocalData.556374-9": "请选择开票状态", "hooks.useLocalData.556374-10": "支付状态", "hooks.useLocalData.556374-11": "请选择支付状态", "hooks.useLocalData.556374-12": "序号", "hooks.useLocalData.556374-13": "单号", "hooks.useLocalData.556374-14": "供应商名称", "hooks.useLocalData.556374-15": "供应商编码", "hooks.useLocalData.556374-16": "创建时间", "hooks.useLocalData.556374-17": "创建人", "hooks.useLocalData.556374-18": "批次", "hooks.useLocalData.556374-19": "到货日期", "hooks.useLocalData.556374-20": "配送仓库", "hooks.useLocalData.556374-21": "入库时间", "hooks.useLocalData.556374-22": "商品编码", "hooks.useLocalData.556374-23": "商品分类", "hooks.useLocalData.556374-24": "采购单位", "hooks.useLocalData.556374-25": "含税单价", "hooks.useLocalData.556374-26": "元", "hooks.useLocalData.556374-27": "税率%", "hooks.useLocalData.556374-28": "不含税单价", "hooks.useLocalData.556374-29": "数量", "hooks.useLocalData.556374-30": "不含税总价", "hooks.useLocalData.556374-31": "采购总价", "hooks.useLocalData.556374-32": "税金", "hooks.useLocalData.556374-33": "抵扣税率%", "hooks.useLocalData.556374-34": "抵扣税额", "hooks.useLocalData.556374-35": "抵扣后成本单价", "hooks.useLocalData.556374-36": "抵扣后成本金额"}, "en": {"hooks.useLocalData.556374-0": "Document date", "hooks.useLocalData.556374-1": "Start Time", "hooks.useLocalData.556374-2": "End time", "hooks.useLocalData.556374-3": "Completion time", "hooks.useLocalData.556374-4": "Document type", "hooks.useLocalData.556374-5": "Please select the document type", "hooks.useLocalData.556374-6": "Product name", "hooks.useLocalData.556374-7": "Please enter the product name", "hooks.useLocalData.556374-8": "Invoice status", "hooks.useLocalData.556374-9": "Please select the invoicing status", "hooks.useLocalData.556374-10": "payment status", "hooks.useLocalData.556374-11": "Please select payment status", "hooks.useLocalData.556374-12": "Serial Number", "hooks.useLocalData.556374-13": "odd numbers", "hooks.useLocalData.556374-14": "Supplier Name", "hooks.useLocalData.556374-15": "supplier code", "hooks.useLocalData.556374-16": "Creation time", "hooks.useLocalData.556374-17": "founder", "hooks.useLocalData.556374-18": "batch", "hooks.useLocalData.556374-19": "Delivery date", "hooks.useLocalData.556374-20": "Delivery warehouse", "hooks.useLocalData.556374-21": "Storage time", "hooks.useLocalData.556374-22": "Product code", "hooks.useLocalData.556374-23": "CATEGORY", "hooks.useLocalData.556374-24": "Purchasing unit", "hooks.useLocalData.556374-25": "Unit price including tax", "hooks.useLocalData.556374-26": "first", "hooks.useLocalData.556374-27": "Tax rate%", "hooks.useLocalData.556374-28": "Unit price excluding tax", "hooks.useLocalData.556374-29": "quantity", "hooks.useLocalData.556374-30": "Total price excluding tax", "hooks.useLocalData.556374-31": "Total purchase price", "hooks.useLocalData.556374-32": "Taxes", "hooks.useLocalData.556374-33": "Deduction tax rate%", "hooks.useLocalData.556374-34": "Tax credit ", "hooks.useLocalData.556374-35": "Unit price of cost after deduction", "hooks.useLocalData.556374-36": "Cost amount after deduction"}}