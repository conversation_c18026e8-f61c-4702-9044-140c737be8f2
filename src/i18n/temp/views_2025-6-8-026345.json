{"zh": {"components.Register.263027-0": "注册账号", "components.Register.263027-1": "已有账号？", "components.Register.263027-2": "去登录", "components.Register.263027-3": "手机号", "components.Register.263027-4": "验证码", "components.Register.263027-5": "获取验证码", "components.Register.263027-6": "请输入密码", "components.Register.263027-7": "我已阅读并同意", "components.Register.263027-8": "服务协议", "components.Register.263027-9": "和", "components.Register.263027-10": "隐私声明", "components.Register.263027-11": "注册", "components.Register.263027-12": "密码不能小于5位", "components.Register.263027-13": "请输入手机号", "components.Register.263027-14": "请输入正确的手机号", "components.Register.263027-15": "请输入验证码", "components.Register.263027-16": "请输入正确的验证码"}, "en": {"components.Register.263027-0": "Register an account", "components.Register.263027-1": "Do you already have an account?", "components.Register.263027-2": "Go login", "components.Register.263027-3": "cell-phone number", "components.Register.263027-4": "Verification code", "components.Register.263027-5": "Obtain verification code", "components.Register.263027-6": "Please input a password", "components.Register.263027-7": "I have read and agree", "components.Register.263027-8": "service agreement", "components.Register.263027-9": "and", "components.Register.263027-10": "privacy statement", "components.Register.263027-11": "register", "components.Register.263027-12": "Password cannot be less than 5 digits", "components.Register.263027-13": "Please enter your phone number", "components.Register.263027-14": "Please enter the correct phone number", "components.Register.263027-15": "Please enter the verification code", "components.Register.263027-16": "Please enter the correct verification code"}}