{"zh": {"hooks.useLocalData.407874-0": "单据日期", "hooks.useLocalData.407874-1": "开始时间", "hooks.useLocalData.407874-2": "结束时间", "hooks.useLocalData.407874-3": "完成时间", "hooks.useLocalData.407874-4": "商品", "hooks.useLocalData.407874-5": "请输入商品", "hooks.useLocalData.407874-6": "序号", "hooks.useLocalData.407874-7": "商品编码", "hooks.useLocalData.407874-8": "商品分类", "hooks.useLocalData.407874-9": "规格编码", "hooks.useLocalData.407874-10": "销售单位", "hooks.useLocalData.407874-11": "销售数量", "hooks.useLocalData.407874-12": "含税总价", "hooks.useLocalData.407874-13": "元", "hooks.useLocalData.407874-14": "不含税总价", "hooks.useLocalData.407874-15": "税额", "hooks.useLocalData.407874-16": "成本总价", "hooks.useLocalData.407874-17": "毛利", "hooks.useLocalData.407874-18": "毛利率"}, "en": {"hooks.useLocalData.407874-0": "Document date", "hooks.useLocalData.407874-1": "Start Time", "hooks.useLocalData.407874-2": "End Time", "hooks.useLocalData.407874-3": "Completion time", "hooks.useLocalData.407874-4": "Product name", "hooks.useLocalData.407874-5": "Please enter the product name", "hooks.useLocalData.407874-6": "Serial Number", "hooks.useLocalData.407874-7": "Product code", "hooks.useLocalData.407874-8": "CATEGORY", "hooks.useLocalData.407874-9": "Specification code", "hooks.useLocalData.407874-10": "Sales Unit", "hooks.useLocalData.407874-11": "sales volumes", "hooks.useLocalData.407874-12": "Total price including tax", "hooks.useLocalData.407874-13": "first", "hooks.useLocalData.407874-14": "Total price excluding tax", "hooks.useLocalData.407874-15": "Tax amount", "hooks.useLocalData.407874-16": "Total cost", "hooks.useLocalData.407874-17": "gross margin", "hooks.useLocalData.407874-18": "gross margin"}}