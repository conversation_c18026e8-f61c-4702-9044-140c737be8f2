{"zh": {"hooks.useOptions.420306-0": "商品", "hooks.useOptions.420306-1": "仓库", "hooks.useOptions.420306-2": "采购类型", "hooks.useOptions.420306-3": "供应商送货", "hooks.useOptions.420306-4": "客户采购", "hooks.useOptions.420306-5": "供应商", "hooks.useOptions.420306-6": "订单数", "hooks.useOptions.420306-7": "订单汇总量", "hooks.useOptions.420306-8": "按采购单位展示", "hooks.useOptions.420306-9": "可用库存", "hooks.useOptions.420306-10": "1、商品真实库存", "hooks.useOptions.420306-11": "2、按基础单位汇总", "hooks.useOptions.420306-12": "采购汇总量", "hooks.useOptions.420306-13": "1、按采购单位展示", "hooks.useOptions.420306-14": "2、采汇总量=（订单汇总量-可用库存）", "hooks.useOptions.420306-15": "*采购单位与基础单位的系数", "hooks.useOptions.420306-16": "已生成采购订单数量", "hooks.useOptions.420306-17": "待采购量", "hooks.useOptions.420306-18": "1、按采购单位统计", "hooks.useOptions.420306-19": "2、待采量=汇总采购量-已生成采购订单数量", "hooks.useOptions.420306-20": "送货日期", "hooks.useOptions.420306-21": "请选择送货日期", "hooks.useOptions.420306-22": "商城类目", "hooks.useOptions.420306-23": "请选择货品三级分类", "hooks.useOptions.420306-24": "批量生成采购单", "hooks.useOptions.420306-25": "确认将{0}条记录生成{1}个采购单？", "hooks.useOptions.420306-26": "生成确认", "hooks.useOptions.420306-27": "确定", "hooks.useOptions.420306-28": "取消"}, "en": {"hooks.useOptions.420306-0": "Product name", "hooks.useOptions.420306-1": "warehouse", "hooks.useOptions.420306-2": "Procurement type", "hooks.useOptions.420306-3": "Supplier delivery", "hooks.useOptions.420306-4": "Customer procurement", "hooks.useOptions.420306-5": "supplier", "hooks.useOptions.420306-6": "Number of orders", "hooks.useOptions.420306-7": "Total amount of order remittance", "hooks.useOptions.420306-8": "Display by purchasing unit", "hooks.useOptions.420306-9": "Available Stock", "hooks.useOptions.420306-10": "1. Real inventory of goods", "hooks.useOptions.420306-11": "2. Summary by Basic Unit", "hooks.useOptions.420306-12": "Total amount of procurement remittance", "hooks.useOptions.420306-13": "1. Display by purchasing unit", "hooks.useOptions.420306-14": "2. Total amount of foreign exchange=(total amount of orders - available inventory)", "hooks.useOptions.420306-15": "*Coefficient between purchasing unit and base unit", "hooks.useOptions.420306-16": "Quantity of purchase orders generated", "hooks.useOptions.420306-17": "Pending purchase quantity", "hooks.useOptions.420306-18": "1. Statistics by purchasing unit", "hooks.useOptions.420306-19": "2. Pending purchase quantity=Summary purchase quantity - Number of generated purchase orders", "hooks.useOptions.420306-20": "Delivery date", "hooks.useOptions.420306-21": "Please select the delivery date", "hooks.useOptions.420306-22": "Mall category", "hooks.useOptions.420306-23": "Please select the third level classification of goods", "hooks.useOptions.420306-24": "Batch generate purchase orders", "hooks.useOptions.420306-25": "Are you sure to generate {1} purchase orders from {0} records?", "hooks.useOptions.420306-26": "Generate confirmation", "hooks.useOptions.420306-27": "sure", "hooks.useOptions.420306-28": "cancel"}}