{"zh": {"components.add.250356-0": "取消", "components.add.250356-1": "确定", "components.add.250356-2": "编辑", "components.add.250356-3": "新增", "components.add.250356-4": "仓库名称", "components.add.250356-5": "仓库名称不能为空", "components.add.250356-6": "仓库编码", "components.add.250356-7": "仓库编码不能为空", "components.add.250356-8": "联系人", "components.add.250356-9": "联系人不能为空", "components.add.250356-10": "联系人电话", "components.add.250356-11": "联系人电话不能为空", "components.add.250356-12": "详细地址", "components.add.250356-13": "详细地址不能为空", "components.add.250356-14": "是否默认", "components.add.250356-15": "备注", "components.add.250356-16": "请输入备注", "components.add.250356-17": "备注不能超过200字符"}, "en": {"components.add.250356-0": "cancel", "components.add.250356-1": "determine", "components.add.250356-2": "edit", "components.add.250356-3": "newly added", "components.add.250356-4": "Warehouse name", "components.add.250356-5": "Warehouse name cannot be empty", "components.add.250356-6": "Warehouse code", "components.add.250356-7": "Warehouse code cannot be empty", "components.add.250356-8": "contacts", "components.add.250356-9": "Contact person cannot be empty", "components.add.250356-10": "CONTACT PHONE", "components.add.250356-11": "Contact phone number cannot be empty", "components.add.250356-12": "detailed address", "components.add.250356-13": "The detailed address cannot be empty", "components.add.250356-14": "Is it default", "components.add.250356-15": "notes", "components.add.250356-16": "Please enter a note", "components.add.250356-17": "Remarks cannot exceed 200 characters"}}