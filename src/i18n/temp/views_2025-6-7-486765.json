{"zh": {"hooks.useOptions.867101-0": "售后退款", "hooks.useOptions.867101-1": "签收退款", "hooks.useOptions.867101-2": "取消订单", "hooks.useOptions.867101-3": "已完成", "hooks.useOptions.867101-4": "已关闭", "hooks.useOptions.867101-5": "待处理", "hooks.useOptions.867101-6": "已驳回", "hooks.useOptions.867101-7": "账期支付", "hooks.useOptions.867101-8": "在线支付", "hooks.useOptions.867101-9": "积分支付", "hooks.useOptions.867101-10": "单据日期", "hooks.useOptions.867101-11": "开始时间", "hooks.useOptions.867101-12": "结束时间", "hooks.useOptions.867101-13": "申请日期", "hooks.useOptions.867101-14": "客户名称", "hooks.useOptions.867101-15": "退货订单号", "hooks.useOptions.867101-16": "源订单号", "hooks.useOptions.867101-17": "退款原因", "hooks.useOptions.867101-18": "关联入库单号", "hooks.useOptions.867101-19": "是否已入库", "hooks.useOptions.867101-20": "是", "hooks.useOptions.867101-21": "否", "hooks.useOptions.867101-22": "退款类型", "hooks.useOptions.867101-23": "序号", "hooks.useOptions.867101-24": "申请提交时间", "hooks.useOptions.867101-25": "客户", "hooks.useOptions.867101-26": "源订单", "hooks.useOptions.867101-27": "售后状态", "hooks.useOptions.867101-28": "退款金额", "hooks.useOptions.867101-29": "仓库", "hooks.useOptions.867101-30": "关联入库单", "hooks.useOptions.867101-31": "入库时间", "hooks.useOptions.867101-32": "处理结果", "hooks.useOptions.867101-33": "退款方式", "hooks.useOptions.867101-34": "操作"}, "en": {"hooks.useOptions.867101-0": "After sales refund", "hooks.useOptions.867101-1": "Sign for refund", "hooks.useOptions.867101-2": "cancellation of order", "hooks.useOptions.867101-3": "Completed", "hooks.useOptions.867101-4": "Closed", "hooks.useOptions.867101-5": "Pending processing", "hooks.useOptions.867101-6": "Rejected", "hooks.useOptions.867101-7": "Payment terms", "hooks.useOptions.867101-8": "Online payment", "hooks.useOptions.867101-9": "Integral payment", "hooks.useOptions.867101-10": "Document date", "hooks.useOptions.867101-11": "Start Time", "hooks.useOptions.867101-12": "End Time", "hooks.useOptions.867101-13": "Application date", "hooks.useOptions.867101-14": "Customer Name", "hooks.useOptions.867101-15": "Return order number", "hooks.useOptions.867101-16": "Source order number", "hooks.useOptions.867101-17": "Reason for refund", "hooks.useOptions.867101-18": "Associated warehouse receipt number", "hooks.useOptions.867101-19": "Has it been stored in the warehouse", "hooks.useOptions.867101-20": "yes", "hooks.useOptions.867101-21": "deny", "hooks.useOptions.867101-22": "Refund type", "hooks.useOptions.867101-23": "Serial number", "hooks.useOptions.867101-24": "Application submission time", "hooks.useOptions.867101-25": "customer", "hooks.useOptions.867101-26": "Source order", "hooks.useOptions.867101-27": "After sales status", "hooks.useOptions.867101-28": "refund amount", "hooks.useOptions.867101-29": "warehouse", "hooks.useOptions.867101-30": "Associated warehouse receipt", "hooks.useOptions.867101-31": "Storage time", "hooks.useOptions.867101-32": "Processing results", "hooks.useOptions.867101-33": "Refund method", "hooks.useOptions.867101-34": "operate"}}