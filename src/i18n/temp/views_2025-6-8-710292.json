{"zh": {"hooks.useLocalData.102279-0": "单据日期", "hooks.useLocalData.102279-1": "开始时间", "hooks.useLocalData.102279-2": "结束时间", "hooks.useLocalData.102279-3": "完成时间", "hooks.useLocalData.102279-4": "商品", "hooks.useLocalData.102279-5": "请输入商品", "hooks.useLocalData.102279-6": "序号", "hooks.useLocalData.102279-7": "商品编码", "hooks.useLocalData.102279-8": "商品分类", "hooks.useLocalData.102279-9": "采购单位", "hooks.useLocalData.102279-10": "数量", "hooks.useLocalData.102279-11": "不含税总价", "hooks.useLocalData.102279-12": "元", "hooks.useLocalData.102279-13": "采购总价", "hooks.useLocalData.102279-14": "税金"}, "en": {"hooks.useLocalData.102279-0": "Document date", "hooks.useLocalData.102279-1": "Start Time", "hooks.useLocalData.102279-2": "End time", "hooks.useLocalData.102279-3": "Completion time", "hooks.useLocalData.102279-4": "Product Name", "hooks.useLocalData.102279-5": "Please enter the product name", "hooks.useLocalData.102279-6": "Serial Number", "hooks.useLocalData.102279-7": "Product code", "hooks.useLocalData.102279-8": "Product classification", "hooks.useLocalData.102279-9": "Purchasing unit", "hooks.useLocalData.102279-10": "number", "hooks.useLocalData.102279-11": "Total price excluding tax", "hooks.useLocalData.102279-12": "first", "hooks.useLocalData.102279-13": "Total purchase price", "hooks.useLocalData.102279-14": "taxes"}}