{"zh": {"hooks.useLocalData.611495-0": "单据日期", "hooks.useLocalData.611495-1": "开始时间", "hooks.useLocalData.611495-2": "结束时间", "hooks.useLocalData.611495-3": "完成时间", "hooks.useLocalData.611495-4": "商品", "hooks.useLocalData.611495-5": "请输入商品", "hooks.useLocalData.611495-6": "序号", "hooks.useLocalData.611495-7": "商品编码", "hooks.useLocalData.611495-8": "商品分类", "hooks.useLocalData.611495-9": "规格编码", "hooks.useLocalData.611495-10": "销售单位", "hooks.useLocalData.611495-11": "销售数量", "hooks.useLocalData.611495-12": "含税总价", "hooks.useLocalData.611495-13": "元", "hooks.useLocalData.611495-14": "不含税总价", "hooks.useLocalData.611495-15": "税额", "hooks.useLocalData.611495-16": "成本总价", "hooks.useLocalData.611495-17": "毛利", "hooks.useLocalData.611495-18": "毛利率"}, "en": {"hooks.useLocalData.611495-0": "Document date", "hooks.useLocalData.611495-1": "Start Time", "hooks.useLocalData.611495-2": "End time", "hooks.useLocalData.611495-3": "Completion time", "hooks.useLocalData.611495-4": "Product name", "hooks.useLocalData.611495-5": "Please enter the product name", "hooks.useLocalData.611495-6": "Serial Number", "hooks.useLocalData.611495-7": "Product code", "hooks.useLocalData.611495-8": "Product classification", "hooks.useLocalData.611495-9": "Specification code", "hooks.useLocalData.611495-10": "Sales Unit", "hooks.useLocalData.611495-11": "sales volumes", "hooks.useLocalData.611495-12": "Total price including tax", "hooks.useLocalData.611495-13": "element", "hooks.useLocalData.611495-14": "Total price excluding tax", "hooks.useLocalData.611495-15": "Tax amount", "hooks.useLocalData.611495-16": "Total cost", "hooks.useLocalData.611495-17": "gross margin", "hooks.useLocalData.611495-18": "gross margin"}}