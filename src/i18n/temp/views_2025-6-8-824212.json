{"zh": {"hooks.useLocalData.241443-0": "单据日期", "hooks.useLocalData.241443-1": "开始时间", "hooks.useLocalData.241443-2": "结束时间", "hooks.useLocalData.241443-3": "完成时间", "hooks.useLocalData.241443-4": "单号", "hooks.useLocalData.241443-5": "请输入单号", "hooks.useLocalData.241443-6": "单据类型", "hooks.useLocalData.241443-7": "请选择单据类型", "hooks.useLocalData.241443-8": "客户名称", "hooks.useLocalData.241443-9": "请输入客户名称", "hooks.useLocalData.241443-10": "商品", "hooks.useLocalData.241443-11": "请输入商品", "hooks.useLocalData.241443-12": "开票状态", "hooks.useLocalData.241443-13": "请选择开票状态", "hooks.useLocalData.241443-14": "支付状态", "hooks.useLocalData.241443-15": "请选择支付状态", "hooks.useLocalData.241443-16": "序号", "hooks.useLocalData.241443-17": "客户分类", "hooks.useLocalData.241443-18": "收货人", "hooks.useLocalData.241443-19": "联系电话", "hooks.useLocalData.241443-20": "收货地址", "hooks.useLocalData.241443-21": "送货日期", "hooks.useLocalData.241443-22": "送货时段", "hooks.useLocalData.241443-23": "仓库", "hooks.useLocalData.241443-24": "订单来源", "hooks.useLocalData.241443-25": "线路", "hooks.useLocalData.241443-26": "支付方式", "hooks.useLocalData.241443-27": "司机", "hooks.useLocalData.241443-28": "创建人", "hooks.useLocalData.241443-29": "创建时间", "hooks.useLocalData.241443-30": "商品编码", "hooks.useLocalData.241443-31": "商品分类", "hooks.useLocalData.241443-32": "销售单位", "hooks.useLocalData.241443-33": "含税单价", "hooks.useLocalData.241443-34": "元", "hooks.useLocalData.241443-35": "税率", "hooks.useLocalData.241443-36": "不含税单价", "hooks.useLocalData.241443-37": "数量", "hooks.useLocalData.241443-38": "不含税总价", "hooks.useLocalData.241443-39": "税额", "hooks.useLocalData.241443-40": "含税总价", "hooks.useLocalData.241443-41": "成本总价", "hooks.useLocalData.241443-42": "毛利", "hooks.useLocalData.241443-43": "毛利率"}, "en": {"hooks.useLocalData.241443-0": "Document date", "hooks.useLocalData.241443-1": "Start Time", "hooks.useLocalData.241443-2": "End Time", "hooks.useLocalData.241443-3": "Completion time", "hooks.useLocalData.241443-4": "odd numbers", "hooks.useLocalData.241443-5": "Please enter the tracking number", "hooks.useLocalData.241443-6": "Document type", "hooks.useLocalData.241443-7": "Please select the document type", "hooks.useLocalData.241443-8": "Customer Name", "hooks.useLocalData.241443-9": "Please enter the customer name", "hooks.useLocalData.241443-10": "Product name", "hooks.useLocalData.241443-11": "Please enter the product name", "hooks.useLocalData.241443-12": "Invoice status", "hooks.useLocalData.241443-13": "Please select the invoicing status", "hooks.useLocalData.241443-14": "payment status", "hooks.useLocalData.241443-15": "Please select payment status", "hooks.useLocalData.241443-16": "Serial Number", "hooks.useLocalData.241443-17": "customer classification", "hooks.useLocalData.241443-18": "consignee", "hooks.useLocalData.241443-19": "contact number", "hooks.useLocalData.241443-20": "Receiving address", "hooks.useLocalData.241443-21": "DELIVERY DATE", "hooks.useLocalData.241443-22": "Delivery Time", "hooks.useLocalData.241443-23": "warehouse", "hooks.useLocalData.241443-24": "ORDER SOURCE", "hooks.useLocalData.241443-25": "line", "hooks.useLocalData.241443-26": "Payment method", "hooks.useLocalData.241443-27": "driver", "hooks.useLocalData.241443-28": "founder", "hooks.useLocalData.241443-29": "Creation time", "hooks.useLocalData.241443-30": "product code", "hooks.useLocalData.241443-31": "Product classification", "hooks.useLocalData.241443-32": "Sales Unit", "hooks.useLocalData.241443-33": "Unit price including tax", "hooks.useLocalData.241443-34": "first", "hooks.useLocalData.241443-35": "tax rate", "hooks.useLocalData.241443-36": "Unit price excluding tax", "hooks.useLocalData.241443-37": "number", "hooks.useLocalData.241443-38": "Total price excluding tax", "hooks.useLocalData.241443-39": "Tax amount", "hooks.useLocalData.241443-40": "Total price including tax", "hooks.useLocalData.241443-41": "Total cost", "hooks.useLocalData.241443-42": "Gross profit", "hooks.useLocalData.241443-43": "gross margin"}}