{"zh": {"hooks.useLocalData.223841-0": "单据日期", "hooks.useLocalData.223841-1": "开始时间", "hooks.useLocalData.223841-2": "结束时间", "hooks.useLocalData.223841-3": "完成时间", "hooks.useLocalData.223841-4": "供应商名称", "hooks.useLocalData.223841-5": "请输入供应商名称", "hooks.useLocalData.223841-6": "商品", "hooks.useLocalData.223841-7": "请输入商品", "hooks.useLocalData.223841-8": "序号", "hooks.useLocalData.223841-9": "供应商编码", "hooks.useLocalData.223841-10": "商品编码", "hooks.useLocalData.223841-11": "商品分类", "hooks.useLocalData.223841-12": "采购单位", "hooks.useLocalData.223841-13": "数量", "hooks.useLocalData.223841-14": "不含税总价", "hooks.useLocalData.223841-15": "元", "hooks.useLocalData.223841-16": "采购总价", "hooks.useLocalData.223841-17": "税金"}, "en": {"hooks.useLocalData.223841-0": "Document date", "hooks.useLocalData.223841-1": "Start Time", "hooks.useLocalData.223841-2": "End time", "hooks.useLocalData.223841-3": "Completion time", "hooks.useLocalData.223841-4": "Supplier Name", "hooks.useLocalData.223841-5": "Please enter the supplier name", "hooks.useLocalData.223841-6": "Product Name", "hooks.useLocalData.223841-7": "Please enter the product name", "hooks.useLocalData.223841-8": "Serial number", "hooks.useLocalData.223841-9": "supplier code", "hooks.useLocalData.223841-10": "product code", "hooks.useLocalData.223841-11": "Product classification", "hooks.useLocalData.223841-12": "Purchasing unit", "hooks.useLocalData.223841-13": "quantity", "hooks.useLocalData.223841-14": "Total price excluding tax", "hooks.useLocalData.223841-15": "first", "hooks.useLocalData.223841-16": "Total purchase price", "hooks.useLocalData.223841-17": "taxes"}}