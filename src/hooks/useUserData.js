import { setFrontExt } from '/@/api/admin/user';
import { useUserInfo } from '/@/stores/userInfo';
export function useUserData() {
	const userInfoStore = useUserInfo();
	const getData = (key) => {
		const userInfo = userInfoStore.userInfos.user;
		return userInfo?.frontExt?.[key];
	};

	const setData = async (key, value) => {
		const params = {
			userId: userInfoStore.userInfos.user.userId,
			frontExtMap: {
				[key]: value,
			},
		};
		return setFrontExt(params).then(() => {
			userInfoStore.setUserInfos();
		});
	};

	return {
		getData,
		setData,
	};
}
