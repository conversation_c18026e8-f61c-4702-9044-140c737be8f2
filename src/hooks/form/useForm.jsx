import { ref } from 'vue';

export function useForm() {
  const form = ref({});
  const formRef = ref(null);
  const config = {
    rowProps: { gutter: 16 },
    colProps: { span: 24 },
  };
  function resetForm() {
    form.value = {};
  }
  async function validateBasicForm() {
    const formEl = formRef.value.elForm;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        await formEl.validate();
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  }
  function clearValidate() {
    const formEl = formRef.value.elForm;
    formEl.clearValidate();
  }
  function setForm(params = {}) {
    const innerParams = {
      ...params,
    };
    form.value = { ...form.value, ...innerParams };
  }
  return {
    form,
    formRef,
    config,
    resetForm,
    setForm,
    validateBasicForm,
    clearValidate,
  };
}
