import { ref, onMounted } from 'vue';
import getShopId from '/@/hooks/shopId';
import { postLineManagePage } from '@/views/mall/supplier/api';

export default function useSupplier() {
	const lineList = ref([]);
	const fetchLineList = async (params) => {
		const res = await postLineManagePage({
			...params,
		});
		lineList.value = res?.data?.records || [];
	};
	const fetchLineListByQuery = (query) => {
		fetchLineList({
			shopId: getShopId(),
			status: 'ENABLE',
			lineName: query || '',
		});
	};
	return {
		lineList,
		fetchLineList,
		fetchLineListByQuery,
	};
}
