import { ref } from 'vue';
import getShopId from '/@/hooks/shopId';
import { postSupplierPage } from '@/views/mall/supplier/api';

export default function useSupplier() {
	const supplierList = ref([]);
	const fetchSupplierList = async (params) => {
		const res = await postSupplierPage({
			...params,
		});
		supplierList.value = res?.data?.records || [];
	};
	const fetchSupplierListByQuery = (query) => {
		fetchSupplierList({
			shopId: getShopId(),
			status: 1,
			name: query || '',
		});
	};
	return {
		supplierList,
		fetchSupplierList,
		fetchSupplierListByQuery,
	};
}
