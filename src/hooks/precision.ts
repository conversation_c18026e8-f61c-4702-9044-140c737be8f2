import { ref } from 'vue';
import { getConfig } from '@/api/mall/mallconfig';
import { camelCase } from 'lodash';
export default function usePrecisionConfig() {
	const precisionConfig = ref({});
	async function getPrecisionConfig() {
		try {
			const res = await getConfig();
			precisionConfig.value = parseConfig(res.data);
		} catch (error) {
			precisionConfig.value = {};
		}
	}
	function parseConfig(configData) {
		const DEFAULT_PRECISION = 2;
		configData = configData || {};
		let config = {};
		for (let [key, value] of Object.entries(configData)) {
			key = camelCase(key);
			value = value || '';
			config[key] = +value.split(':')[1] || DEFAULT_PRECISION;
		}
		return config;
	}

	return {
		precisionConfig,
		getPrecisionConfig,
	};
}
