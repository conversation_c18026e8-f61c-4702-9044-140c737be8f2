import { ref, onMounted } from 'vue';
import { getGoodsList } from '/@/views/mall/api';

export default function useSkus() {
	const goodsList = ref([]);
	const fetchGoodsList = async (params) => {
		const res = await getGoodsList({
			...params,
		});

		goodsList.value = res?.data?.records || [];
	};
	const fetchGoodsListByQuery = (query) => {
		fetchGoodsList({
			queryEnable: '1',
			excludeBom: '1',
			whetherPurchaseUnit: 'YES',
			spuName: query || '',
			current: 1,
			size: 20,
		});
	};
	return {
		goodsList,
		fetchGoodsList,
		fetchGoodsListByQuery,
	};
}
