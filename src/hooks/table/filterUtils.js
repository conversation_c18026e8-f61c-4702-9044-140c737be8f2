import { dateFormatter } from './utils';
/**
 * 表头筛选格式化
 * @param status
 * @param options
 * @param labelField
 * @param valueField
 * @returns {*}
 */
export function commonFormatTableFilter({ options = [], labelField = 'label', valueField = 'value' }, status) {
	const result = options.find((item) => item[valueField] === status);
	if (result) {
		return result[labelField];
	}
	return undefined;
}

export function radioGroupHeader({ options = [], labelField = 'label', valueField = 'value' }) {
	return {
		component: 'el-radio-group',
		hasAll: true,
		componentAttrs: {
			clearable: true,
		},
		options,
		formatter: commonFormatTableFilter.bind(null, { options, labelField, valueField }),
	};
}

export function selectHeader({ options = [], labelField = 'label', valueField = 'value', attrs }) {
	return {
		component: 'el-select',
		hasAll: true,
		componentAttrs: {
			clearable: true,
			...attrs,
		},
		options,
		formatter: attrs?.multiple ? undefined : commonFormatTableFilter.bind(null, { options, labelField, valueField }),
	};
}

export function dateTimePickerHeader(options = {}) {
	const attr = {
		type: 'datetimerange',
		startPlaceholder: '开始时间',
		endPlaceholder: '结束时间',
		rangeSeparator: '至',
		placeholder: '请选择日期时间',
		defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
		format: 'YYYY-MM-DD HH:mm:ss',
		valueFormat: 'YYYY-MM-DD HH:mm:ss',
		...options,
	};
	return {
		component: 'el-date-picker',
		componentAttrs: {
			...attr,
		},
		colAttrs: {
			lg: 6,
			md: 6,
			sm: 6,
			xl: 6,
			xs: 6,
		},
		formatter: (value) => {
			if (typeof value === 'object') {
				return value.map((item) => dateFormatter(item)).join('至');
			}
			return dateFormatter(value);
		},
	};
}

export function datePickerHeader(options = {}) {
	const attr = {
		type: 'daterange',
		startPlaceholder: '开始时间',
		endPlaceholder: '结束时间',
		rangeSeparator: '至',
		placeholder: '请选择日期',
		format: 'YYYY-MM-DD',
		valueFormat: 'YYYY-MM-DD',
		...options,
	};
	return {
		component: 'el-date-picker',
		componentAttrs: {
			...attr,
		},
		colAttrs: {
			lg: 6,
			md: 6,
			sm: 6,
			xl: 6,
			xs: 6,
		},
		formatter: (value) => {
			if (typeof value === 'object') {
				return value.map((item) => dateFormatter(item)).join('至');
			}
			return dateFormatter(value, 'YYYY-MM-DD');
		},
	};
}

export function inputHeader(placeholder) {
	return {
		component: 'el-input',
		componentAttrs: {
			placeholder: placeholder || '请输入',
			defaultTime: ['00:00:00', '23:59:59'],
		},
		formatter: (text) => text,
	};
}
