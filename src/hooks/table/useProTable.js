import { ref } from 'vue';
import { clearReqEmptyField, getRef, formatDate } from './utils';
import { loadSearchPlus } from './plugins/searchPlus';
import { loadGetPlus } from './plugins/loadGetPlus';

export function useProTable(options) {
	const {
		apiFn, // 接口对象
		extParams = {}, // 固定的body入参
		extQuerys = {}, // 固定的url入参
		isClearReqEmptyField = true, // 提交给接口是否删除无效字段
		paramsHandler, // 最终提交给接口的参数劫持方法，用户修改最终提交的参数
		querysHandler, // 最终提交给接口的url参数劫持方法，用户修改最终提交的参数
		responseHandler, // 最终入参给表格里面的数据，用于格式化接口返回不符合前端表格规范
		customTotalHandler, // 分页不规范接口，获取total
		optimize, // 优化性能，一般用于多且复杂字段，每次请求先清空表格数据
		plugins, // 插件挂载，需要才挂载们不需要不传
		paginationConfig = {}, // 分页配置
	} = options;
	const tableData = ref([]);
	const apiFnRef = getRef(apiFn);
	const extParamsRef = getRef(extParams);
	const extQuerysRef = getRef(extQuerys);
	const proTableRef = ref(null);
	const pagination = ref({
		background: true,
		page: 1,
		size: 20,
		pageSize: 20,
		sort: '',
		layout: 'total, sizes, prev, pager, next, jumper',
		pageSizes: [10, 20, 50, 100, 200, 400],
		total: 0,
		...paginationConfig,
	});
	const filterTableData = ref({});
	const finalPostResult = ref({}); // 处理后最终传给接口的查询条件，一般供导出等使用
	async function remoteMethod({ searchData, filterData, pagination: page }) {
		if (optimize) {
			tableData.value = [];
		}
		const sort = page?.sort || extQuerysRef.value?.sort;
		let querys = {
			...{ size: page?.size || page?.pageSize, current: page?.page },
			...extQuerysRef.value,
			sort,
		};
		let params = {
			...{ size: page?.size || page?.pageSize, current: page?.page },
			...searchData,
			...filterData,
			...extParamsRef.value,
		};
		loadSearchPlus(plugins, params); // 搜索增强插件
		if (isClearReqEmptyField) {
			clearReqEmptyField(params);
		}
		try {
			params = paramsHandler ? await paramsHandler(params) : params;
		} catch (e) {
			// console.log(e);
		}
		querys = querysHandler ? querysHandler(querys) : querys;
		finalPostResult.value = {
			params,
			querys,
		};
		const data = await apiFnRef.value(params, querys);
		const result = data?.data || data;
		pagination.value.total = (result && 'total' in result) || !customTotalHandler ? result?.total : customTotalHandler(data);
		const list = result?.records || result || [];
		const responseHandlerList = responseHandler ? responseHandler(list) : list;
		loadGetPlus(plugins, responseHandlerList); // 取值增强插件
		formatDate(responseHandlerList);
		tableData.value = responseHandlerList;
		return responseHandlerList;
	}

	function reLoad(params) {
		// 刷新列表
		if (params) {
			extParamsRef.value = { ...extParamsRef.value, ...params };
		}
		proTableRef?.value?.getData();
	}

	// 排序
	function sortChangeFunction({ column, prop, order }) {
		if (prop && order) {
			const SORT_TYPE = {
				descending: 'DESC',
				ascending: 'ASC',
			};
			pagination.value.sort = `${column.sortAlias || column.property}__${SORT_TYPE[column.order]}`;
		} else {
			pagination.value.sort = '';
		}
		reLoad();
	}

	const tableProps = {
		transformation: (val) => (val === undefined || val === '' || val === null ? '--' : val),
		showTableSetting: true,
		onSortChange: ({ column, prop, order }) => sortChangeFunction({ column, prop, order }),
		yunPagination: true,
	};

	return {
		tableData,
		proTableRef,
		reLoad,
		pagination,
		remoteMethod,
		tableProps,
		filterTableData,
		finalPostResult,
	};
}
