import { PLUGINS_NAME } from './constant';

export function loadSearchPlus(plugins, params) {
	try {
		if (!plugins || !plugins?.list?.includes(PLUGINS_NAME.SEARCH_PLUS)) {
			return;
		}
		if (plugins.list.includes(PLUGINS_NAME.SEARCH_PLUS)) {
			const columns = plugins?.config?.columns;
			const searchFields = plugins?.config?.searchFields || [];
			// eslint-disable-next-line no-restricted-syntax,guard-for-in
			for (const i in params) {
				const currentSearchColumn = columns.find((item) => item?.prop === i && (item?.filterAlias || item?.fieldMapToTime));
				const currentField = searchFields.find((item) => item?.prop === i && (item?.filterAlias || item?.fieldMapToTime));
				if (currentSearchColumn) {
					// 查找到了
					if (currentSearchColumn.filterAlias) {
						// 存在filter别名
						params[currentSearchColumn.filterAlias] = params[i];
					}
					if (currentSearchColumn.fieldMapToTime) {
						// 存在时间字段映射
						if (params[currentSearchColumn.prop] && Array.isArray(params[currentSearchColumn.prop])) {
							// eslint-disable-next-line prefer-destructuring
							params[currentSearchColumn.fieldMapToTime[0]] = params[i][0];
							// eslint-disable-next-line prefer-destructuring
							params[currentSearchColumn.fieldMapToTime[1]] = params[i][1];
						}
					}
					delete params[i];
				}
				if (currentField) {
					// 查找到了
					if (currentField.filterAlias) {
						// 存在filter别名
						params[currentField.filterAlias] = params[i];
					}
					if (currentField.fieldMapToTime) {
						// 存在时间字段映射
						if (params[currentField.prop] && Array.isArray(params[currentField.prop])) {
							// eslint-disable-next-line prefer-destructuring
							params[currentField.fieldMapToTime[0]] = params[i][0];
							// eslint-disable-next-line prefer-destructuring
							params[currentField.fieldMapToTime[1]] = params[i][1];
						}
					}
					delete params[i];
				}
			}
		}
	} catch (e) {
		// console.log(e);
	}
}
