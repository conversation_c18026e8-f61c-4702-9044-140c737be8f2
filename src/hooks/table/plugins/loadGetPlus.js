// import get from 'lodash.get';
import { get } from 'lodash';

// 实现一个 lodash.get
// export function get(obj, path, defaultValue) {
// 	return path.split('.').reduce((acc, part) => acc && acc[part], obj) || defaultValue;
// }

export function loadGetPlus(plugins, list) {
	const columns = plugins?.config?.columns;
	try {
		if (columns) {
			// eslint-disable-next-line no-restricted-syntax,guard-for-in
			for (const i in columns) {
				if (columns[i].prop && columns[i].prop.includes('.')) {
					// eslint-disable-next-line no-restricted-syntax,guard-for-in
					for (const j in list) {
						list[j][columns[i].prop] = get(list[j], columns[i].prop);
					}
				}
			}
		}
	} catch (e) {
		// console.log(e);
	}
}
