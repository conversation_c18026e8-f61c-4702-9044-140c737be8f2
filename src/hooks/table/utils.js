import moment from 'moment';
import { isRef, ref } from 'vue';

export const dateFormatter = (time, formatter) => {
	if (time === undefined || time == null) {
		return '';
	}
	return moment(time).format(formatter || 'YYYY-MM-DD HH:mm:ss');
};

/**
 * 清楚请求参数中为空，并返回
 * @param params
 * @returns {*}
 */
export function clearReqEmptyField(params) {
	// eslint-disable-next-line no-restricted-syntax,guard-for-in
	for (const i in params) {
		const data = params[i];
		// eslint-disable-next-line no-mixed-operators
		if (
			data === '' ||
			(Array.isArray(data) && data.length === 0) ||
			data === null ||
			data === undefined ||
			(Object.prototype.toString.call(data) === '[object Object]' && Object.keys(data).length === 0)
		) {
			// eslint-disable-next-line no-param-reassign
			delete params[i];
		}
	}
}

export function getRef(value) {
	return isRef(value) ? value : ref(value);
}

export function formatDate(list) {
	// eslint-disable-next-line no-restricted-syntax,guard-for-in
	for (const i in list) {
		// eslint-disable-next-line no-restricted-syntax
		for (const j in list[i]) {
			if ((String(list[i][j]).length === 10 || String(list[i][j]).length === 13) && (j.includes('time') || j.includes('Time'))) {
				if (list[i][j] && !list[i][j].toString().includes(':')) {
					list[i][j] = moment(list[i][j]).format('YYYY-MM-DD HH:mm:ss');
				}
			}
		}
	}
}
export const isPromise = (obj) => !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function';

export function enumnsToOptions(enumns) {
	const options = [];
	// eslint-disable-next-line no-restricted-syntax,guard-for-in
	for (const i in enumns) {
		options.push({
			label: enumns[i],
			value: i,
		});
	}
	return options;
}

export function enumnsToKeyObject(enumns) {
	const enmusTemp = JSON.parse(JSON.stringify(enumns));
	// eslint-disable-next-line no-restricted-syntax,guard-for-in
	for (const i in enmusTemp) {
		enmusTemp[i] = i;
	}
	return enmusTemp;
}
