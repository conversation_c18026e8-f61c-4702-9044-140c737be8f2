import { ref, onMounted } from 'vue';
import moment from 'moment';
import { clearReqEmptyField } from './utils';
// import { useTableHeight } from '@/hooks/table/useTableHeightResize';

export function useTable(options) {
	// const { tableHeight } = useTableHeight();
	const { api, extQuerys = {} } = options;
	const searchQuerys = ref({
		page: 1,
		size: 30,
		...extQuerys,
	});
	const tableData = ref([]);
	const loading = ref(false);
	const pagination = ref({
		background: true,
		page: 1,
		layout: 'total, sizes, prev, pager, next, jumper',
		pageSizes: [10, 20, 30, 40],
		pageSize: 30,
		size: 30,
		total: 0,
	});
	async function handleSearch(searchParams) {
		try {
			// eslint-disable-next-line max-len
			const params = {
				...searchQuerys.value,
				...searchParams,
				...{ size: pagination.value.size, page: pagination.value.page },
			};
			const querys = {
				size: pagination.value.size,
				page: pagination.value.page,
			};
			clearReqEmptyField(params);
			loading.value = true;
			const data = await api(params, querys);
			pagination.value.page = data.page;
			pagination.value.total = data.total;
			pagination.value.pageSize = data.size;
			const list = data.records;
			// eslint-disable-next-line no-restricted-syntax,guard-for-in
			for (const i in list) {
				// eslint-disable-next-line no-restricted-syntax
				for (const j in list[i]) {
					if (j.includes('time') || j.includes('Time')) {
						if (list[i][j]) {
							list[i][j] = moment(list[i][j]).format('YYYY-MM-DD HH:mm:ss');
						}
					}
				}
			}
			tableData.value = list;
			loading.value = false;
		} catch (e) {
			// console.log(e);
			loading.value = false;
		}
	}
	onMounted(() => {
		handleSearch();
	});
	const transformation = (val) => (val === undefined || val === '' || val === null ? '-' : val);
	return {
		searchQuerys,
		handleSearch,
		tableData,
		loading,
		pagination,
		// tableHeight,
		transformation,
	};
}
