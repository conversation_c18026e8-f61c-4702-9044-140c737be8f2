import { onMounted, onUnmounted, nextTick, ref } from 'vue';
import ResizeObserver from 'resize-observer-polyfill';
import throttleLodash from 'lodash.throttle';

// 单表格页面、高度自适应hooks，多表格暂无法适配
// 需要满足条件：
// 1. .layout-main position: relative;
// 2. yun-table组件的父级定位是layout-main
export function useTableHeight() {
  const tableHeight = ref(350); // 初始高度

  // 表格高度更新方法
  const updateTableHeight = () => {
    document.querySelector('.layout-main').style.position = 'relative';
    const layoutMainHeight = document.querySelector('.layout-main').offsetHeight;
    const dmTableTop = document.querySelector('.dm-table')?.offsetTop;
    const dmTableHeader = document.querySelector('.dm-table-header')?.offsetHeight || 0;
    const dmTableFilter = document.querySelector('.filter-manage-box')?.offsetHeight || 0;
    const resNum = layoutMainHeight - dmTableTop - dmTableHeader - dmTableFilter - 85;
    if (tableHeight.value !== resNum && resNum > 200) {
      tableHeight.value = resNum;
    }
  };

  // 防抖, 立即触发，但防抖
  const throttle = throttleLodash(updateTableHeight, 500, { leading: false, trailing: true });

  let robserverOld = null;
  // 元素监听
  const observeElement = () => {
    const element1 = document.querySelector('.dm-table');
    const robserver = new ResizeObserver((entries) => {
      window.requestAnimationFrame(() => {
        if (!Array.isArray(entries) || !entries.length) {
          return;
        }
        entries.forEach((item) => {
          if (item.target === element1) {
            throttle();
          }
        });
      });
    });
    robserverOld = robserver;

    if (element1) {
      robserver.observe(element1);
    }
  };

  const disconnectEl = () => {
    if (robserverOld) {
      robserverOld.disconnect();
    }
  };

  onMounted(async () => {
    nextTick(() => {
      observeElement();
    });
    window.addEventListener('resize', observeElement);
  });
  // 页面卸载时
  onUnmounted(() => {
    window.removeEventListener('resize', observeElement);
    disconnectEl();
  });

  return {
    tableHeight,
  };
}
