import { ref } from 'vue';
import getShopId from '/@/hooks/shopId';
import { getPage } from '@/api/mall/shopcustomerinfo';

export default function useSupplier() {
	const customerList = ref([]);
	const fetchCustomerList = async (params) => {
		const res = await getPage({
			...params,
		});
		customerList.value = res?.data?.records || [];
	};
	const fetchCustomerListByQuery = (query) => {
		fetchCustomerList({
			shopId: getShopId(),
			enable: 1,
			status: 'ENABLE',
			customerName: query || '',
		});
	};
	return {
		customerList,
		fetchCustomerList,
		fetchCustomerListByQuery,
	};
}
