import { ref, onMounted } from 'vue';
import { getWarehouseInfoPage } from '@/views/mall/supplier/api';

export default function useSupplier() {
	const warehouseList = ref([]);
	const fetchWarehouseList = async (params) => {
		const res = await getWarehouseInfoPage({
			...params,
		});
		warehouseList.value = res?.data?.records || [];
	};
	const fetchWarehouseListByQuery = (query) => {
		fetchWarehouseList({
			status: 1,
			warehouseName: query || '',
			size: 20,
		});
	};
	return {
		warehouseList,
		fetchWarehouseList,
		fetchWarehouseListByQuery,
	};
}
