import request from '/@/utils/request';

export function batchUploadExcel(url, data) {
	return request({
		url,
		method: 'post',
		data,
		responseType: 'blob',
	})
		.then(async (response) => {
			// 获取文件名称
			let defaultName = `${new Date().getTime()}.xlsx`;
			try {
				const disposition = response.headers['content-disposition'] || '';
				defaultName = decodeURIComponent(disposition.split('filename=')[1]);
			} catch (error) {
				// eslint-disable-next-line no-console
				console.log(error);
			}
			const data = response.data;
			const type = data?.type;
			if (type === 'application/vnd.ms-excel') {
				const blob = new Blob([data], { type });
				const url = window.URL.createObjectURL(blob);
				return { code: 0, data: url, msg: null, ok: true, type: 'excel', name: defaultName };
			} else if (type === 'text/html' || type === 'application/json') {
				const text = await data?.text();
				const json = JSON.parse(text);
				return { ...json, type: 'json' };
			}
			return data;
		})
		.catch((err) => err);
}
