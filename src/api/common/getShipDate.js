import { getConfig } from '@/api/mall/shopconfig';
import moment from 'moment';

const getShipDate = async (shopId) => {
	const res = await getConfig({
		shopId: shopId,
		modelKey: 'CUT_OFF_TIME',
		configKey: 'CUT_OFF_TIME',
	});
	let date;
	if (!res.data) {
		//生成默认时间为当天的12:00
		date = new Date();
		date.setHours(12);
		date.setMinutes(0);
		date.setSeconds(0);
	} else {
		const result = res.data.configValue.split(':');
		date = new Date();
		date.setHours(result[0]);
		date.setMinutes(result[1]);
	}

	//获取当前时间是超过date还是date之前
	let now = new Date();
	let nowHours = now.getHours();
	let nowMinutes = now.getMinutes();
	let nowSeconds = now.getSeconds();
	let nowTime = nowHours * 3600 + nowMinutes * 60 + nowSeconds;
	let dateHours = date.getHours();
	let dateMinutes = date.getMinutes();
	let dateSeconds = date.getSeconds();
	let dateTime = dateHours * 3600 + dateMinutes * 60 + dateSeconds;
	if (nowTime > dateTime) {
		date.setDate(date.getDate() + 1);
	}
	//将date转换为时间戳
	//将date的时分秒设置为0
	date.setHours(0);
	date.setMinutes(0);
	date.setSeconds(0);
	date = moment(date).format('YYYY-MM-DD HH:mm:ss');
	return date;
};

export default getShipDate;
