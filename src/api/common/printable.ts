import request from '/@/utils/request';

//获取printId
export function getPrintId(params) {
  return request({
    url: `/jimu/customer/saas/printTemplateDetail/getPrintId`,
    method: 'get',
    params
  })
}

// 获取租户分配的打印模板
export function getTenantPrintTemplateList(params) {
  return request({
    url: `/jimu/customer/saas/printTemplateDetail/getTenantPrintTemplateList`,
    method: 'get',
    params
  })
}

// 添加客户分配模板
export function tenantRelationCustomer(data) {
  return request({
    url: `/jimu/customer/saas/printTemplateDetail/tenantRelationCustomer`,
    method: 'post',
    data
  })
}
