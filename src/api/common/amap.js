
/**
 * 获取枚举
 * @param {*} params 入参
 * @returns
 * https://app.apifox.com/project/2020332
 */

export function getRegeo(params) {
  return new Promise((resolve, reject) => {
    let url = 'https://restapi.amap.com/v3/geocode/regeo?'
    let param = {
      key: '500bf914d1c3f9405754bc0a345521c0',
      ...params,
      output: 'json',
      radius: '0',
      extensions: 'addressComponent'
    }
    for (let p in param) {
      url += `${p}=${param[p]}&`
    }
    fetch(url)
      .then((res) => {
        res
          .json()
          .then((data) => {
            const { addressComponent } = data.regeocode
            const result = {
              provinceName: addressComponent.province,
              provinceCode: `${addressComponent?.adcode?.substring(0, 2)}0000`,
              cityName: addressComponent.city,
              cityCode: addressComponent?.adcode?.substring(0, 4) + '00',
              areaName: addressComponent.district,
              countyCode: addressComponent.adcode
            }
            resolve(result)
          })
          .catch((err) => {
            reject(err)
          })
      })
      .catch((err) => {
        reject(err)
      })
  })
}
