import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/admin/user/page',
		method: 'get',
		params: query,
	});
}

export function getCount(query) {
	return request({
		url: '/admin/user/count',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/admin/user',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/admin/user/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/admin/user/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/admin/user',
		method: 'put',
		data: obj,
	});
}

export function editPassword(obj) {
	return request({
		url: '/admin/user/password',
		method: 'put',
		data: {
			...obj,
		},
	});
}

export function editObj(obj) {
	return request({
		url: '/admin/user/edit',
		method: 'put',
		data: obj,
	});
}

export function bindPhone(obj) {
	return request({
		url: '/admin/user/phone',
		method: 'put',
		data: obj,
	});
}
/**
 *
 * 获取账号列表
 * https://yapi.ops.yunlizhi.cn/project/892/interface/api/62691
 */
export function fetchAccountPage(data) {
	return request({
		url: `/admin/user/account/page?current=${data.current}&size=${data.size}`,
		method: 'post',
		data,
	});
}
/**
 *
 * 账号 -> 启/禁用
 * https://yapi.ops.yunlizhi.cn/project/892/interface/api/62527
 */
export function statusObj(data) {
	return request({
		url: '/admin/user/update/status',
		method: 'post',
		data,
	});
}
/**
 *
 * 日志
 * https://yapi.ops.yunlizhi.cn/project/892/interface/api/62979
 */
export function fetchLogListNew(data) {
	return request({
		url: '/admin/log/list',
		method: 'post',
		data,
	});
}
export function putUserRestPwdV2(obj) {
	return request({
		url: '/admin/user/restPwd/v2',
		method: 'put',
		data: obj,
	});
}
export function addObjV2(obj) {
	return request({
		url: '/admin/user/v2',
		method: 'post',
		data: obj,
	});
}
export function fetchTree(query) {
	return request({
		url: '/admin/dept/tree',
		method: 'get',
		params: query,
	});
}
/**
 * 修改部门排序
 * https://yapi.ops.yunlizhi.cn/project/892/interface/api/75213
 *
 */
export function setDeptSort(data, params = {}) {
	return request({
		url: '/admin/dept/deptSort',
		method: 'post',
		data,
		params,
	});
}
export function delDept(id) {
	return request({
		url: `/admin/dept/${id}`,
		method: 'delete',
	});
}
export function fetchOrgPage(query) {
	return request({
		url: '/saas-freshx-biz/org/page',
		method: 'get',
		params: query,
	});
}
export function postDept(obj) {
	return request({
		url: '/admin/dept',
		method: 'post',
		data: obj,
	});
}
export function putDept(obj) {
	return request({
		url: '/admin/dept',
		method: 'put',
		data: obj,
	});
}
