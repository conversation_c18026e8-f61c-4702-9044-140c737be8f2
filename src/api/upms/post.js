import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/admin/post/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/admin/post',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: `/admin/post/${id}`,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: `/admin/post/${id}`,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/admin/post',
		method: 'put',
		data: obj,
	});
}
export function getPostList() {
	return request({
		url: '/admin/post/list',
		method: 'get',
	});
}
export function setDelFag(data) {
	return request({
		url: '/admin/post/setDelFag',
		method: 'post',
		data,
	});
}
