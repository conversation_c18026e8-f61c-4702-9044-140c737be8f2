import request from '/@/utils/request';

//分页列表
export function getPage(data) {
	return request({
		url: '/wms/CustomerManage/page',
		method: 'post',
		data,
	});
}

export function getPageCustomer(data) {
	return request({
		url: '/wms/CustomerManage/shopCustomerList',
		method: 'post',
		data,
	});
}
//新增
export function addObj(data) {
	return request({
		url: '/wms/lineManage/addLine',
		method: 'post',
		data,
	});
}

//修改
export function putObj(data) {
	return request({
		url: '/mall/goodsDeposit/saveOrUpdate',
		method: 'post',
		data,
	});
}

//删除

export function delObj(id) {
	return request({
		url: '/mall/goodsDeposit/delete/' + id,
		method: 'delete',
	});
}

//获取所有司机
export function getAllDriver(data) {
	return request({
		url: '/wms/driverManage/page',
		method: 'post',
		data,
	});
}

//通过线路id获取司机
export function getDriverByLineId(data) {
	return request({
		url: '/wms/driverLine/listByLineId',
		method: 'post',
		data,
	});
}

//通过司机id解绑司机
export function unbindDriverById(data) {
	return request({
		url: '/wms/driverLine/unbindDriver',
		method: 'post',
		data,
	});
}

//通过线路id获取客户
export function getCustomerByLineId(data) {
	return request({
		url: '/wms/customerLine/listByLineId',
		method: 'post',
		data,
	});
}

//获取线路列表
export function getLineList(data) {
	return request({
		url: '/wms/lineManage/page',
		method: 'post',
		data,
	});
}

//通过线路ID绑定客户
export function bindCustomerByLineId(data) {
	return request({
		url: '/wms/CustomerManage/addCustomer',
		method: 'post',
		data,
	});
}

//通过线路ID解绑客户
export function unbindCustomerByLineId(data) {
	return request({
		url: '/wms/CustomerManage/unbind',
		method: 'post',
		data,
	});
}

// 更改路线绑定客户排序
export function updateSort(data) {
	return request({
		url: '/wms/CustomerManage/updateSort',
		method: 'post',
		data,
	});
}
