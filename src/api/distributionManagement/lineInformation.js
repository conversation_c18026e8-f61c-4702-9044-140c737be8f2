import request from '/@/utils/request';
import getShopId from '/@/hooks/shopId';

//分页列表
export function getPage(data) {
	const shopId = getShopId();
	if (shopId) {
		data.shopId = shopId;
	} else {
		delete data.shopId;
	}
	return request({
		url: '/wms/lineManage/page',
		method: 'post',
		data,
	});
}
//新增
export function addObj(data) {
	return request({
		url: '/wms/lineManage/upsertLine',
		method: 'post',
		data,
	});
}

//修改
export function putObj(data) {
	return request({
		url: '/wms/lineManage/updateLine',
		method: 'post',
		data,
	});
}

//删除

export function delObj(id) {
	return request({
		url: '/mall/goodsDeposit/delete/' + id,
		method: 'delete',
	});
}

//获取所有司机
export function getAllDriver(data) {
	return request({
		url: '/wms/driverManage/page',
		method: 'post',
		data,
	});
}

//通过线路id获取司机
export function getDriverByLineId(data) {
	return request({
		url: '/wms/driverLine/listByLineId',
		method: 'post',
		data,
	});
}

//通过司机id解绑司机
export function unbindDriverById(data) {
	return request({
		url: '/wms/driverLine/remove',
		method: 'post',
		data,
	});
}

//通过线路id获取客户
export function getCustomerByLineId(data) {
	return request({
		url: '/wms/CustomerManage/page',
		method: 'post',
		data,
	});
}

//通过Id禁用线路
export function disableLineById(id) {
	return request({
		url: `/wms/lineManage/disableLine/${id}`,
		method: 'get',
	});
}
