import request from '/@/utils/request';

//分页列表
export function getPageByLine(data) {
	return request({
		url: '/wms/lineManage/schedule/listByLine',
		method: 'post',
		data,
	});
}

export function getPageByOrder(data) {
	return request({
		url: '/wms/lineManage/schedule/listByOrder',
		method: 'post',
		data,
	});
}

//获取统计数据
export function getStatistics(data) {
	return request({
		url: '/wms/lineManage/statistic',
		method: 'post',
		data,
	});
}

//获取打印数据订单
export function getPrintData(data) {
	return request({
		url: '/wms/lineManage/print',
		method: 'post',
		data,
		responseType: 'blob',
	});
}

//获取打印数据合同
export function getPrintContract(data) {
	return request({
		url: '/wms/lineManage/printCus',
		method: 'post',
		data,
		responseType: 'blob',
	});
}
