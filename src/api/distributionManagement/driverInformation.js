import request from '/@/utils/request';
import getShopId from '/@/hooks/shopId';

//分页列表
export function getPage(data) {
	const shopId = getShopId();
	if (shopId) {
		data.shopId = shopId;
	} else {
		delete data.shopId;
	}
	return request({
		url: '/wms/driverManage/page',
		method: 'post',
		data,
	});
}
//新增
export function addObj(data) {
	return request({
		url: '/wms/driverManage/addDriver',
		method: 'post',
		data,
	});
}

//修改
export function putObj(data) {
	return request({
		url: '/wms/driverManage/updateDriver',
		method: 'post',
		data,
	});
}

//删除

export function delObj(id) {
	return request({
		url: '/mall/goodsDeposit/delete/' + id,
		method: 'delete',
	});
}

//获取所有线路
export function getAllRoad(data) {
	return request({
		url: '/wms/lineManage/page',
		method: 'post',
		data,
	});
}

//禁用司机
export function disableDriver(data) {
	return request({
		url: '/wms/driverManage/changeStatus',
		method: 'post',
		data,
	});
}

//通过司机id获取线路
export function getLineByDriverId(data) {
	return request({
		url: '/wms/driverLine/listByDriverId',
		method: 'post',
		data,
	});
}

//通过司机id和线路id解除绑定
export function unbindLine(data) {
	return request({
		url: '/wms/driverLine/remove',
		method: 'post',
		data,
	});
}
