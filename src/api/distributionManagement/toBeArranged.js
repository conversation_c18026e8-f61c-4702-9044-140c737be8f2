import request from '/@/utils/request';

//分页列表
export function getPageByLine(data) {
	return request({
		url: '/wms/lineManage/schedule/listByLine',
		method: 'post',
		data,
	});
}

export function getPageByOrder(data) {
	return request({
		url: '/wms/lineManage/schedule/listByOrder',
		method: 'post',
		data,
	});
}

//获取统计数据
export function getStatistics(data) {
	return request({
		url: '/wms/lineManage/statistic',
		method: 'post',
		data,
	});
}

//生成临时编码
export function getTempCode(data) {
	return request({
		url: '/wms/lineManage/schedule/finish',
		method: 'post',
		data,
	});
}

//更新线路信息
export function updateLine(data) {
	return request({
		url: '/wms/lineManage/updateLine',
		method: 'post',
		data,
	});
}

//变更线路
export function changeBind(data) {
	return request({
		url: '/wms/CustomerManage/changeBind',
		method: 'post',
		data,
	});
}

//获取地图线路列表
export function getMapLineList(data) {
	return request({
		url: '/wms/lineManage/map/lineList',
		method: 'post',
		data,
	});
}

export function getMapLineListHis(data) {
	return request({
		url: '/wms/lineManage/map/lineListHistory',
		method: 'post',
		data,
	});
}

//获取线路司机列表
export function getMapDriverList(shopId) {
	return request({
		url: `/wms/driverLine/driverLineList/${shopId}`,
		method: 'get',
	});
}

//变更当前线路下的司机
export function changeDriver(params) {
	return request({
		url: '/wms/driverLine/freshBind',
		method: 'get',
		params,
	});
}

// 获取订单
export function relationManageMapShow(data) {
	return request({
		url: '/wms/relationManage/mapShow',
		method: 'post',
		data,
	});
}

// 获取线路列表
export function getMapShowList(data) {
	return request({
		url: '/wms/lineManage/mapShowList',
		method: 'post',
		data,
	});
}

// 获取线路绑定的客户
export function getMapShowDetail(data) {
	return request({
		url: '/wms/lineManage/mapShowDetail',
		method: 'post',
		data,
	});
}

// 客户绑定线路
export function lineManageChangeLine(data) {
	return request({
		url: '/wms/lineManage/changeLine',
		method: 'post',
		data,
	});
}

// 排线完成生产编码
export function mapScheduleFinish(data) {
	return request({
		url: '/wms/lineManage/mapScheduleFinish',
		method: 'post',
		data,
	});
}

// 更改默认司机
export function changeDefaultDriver(params) {
	return request({
		url: '/wms/driverLine/changeDefaultDriver',
		method: 'get',
		params,
	});
}
