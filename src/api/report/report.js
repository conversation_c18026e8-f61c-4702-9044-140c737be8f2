import request from '/@/utils/request';
import useShopId from '/@/hooks/shopId';
import { downFile } from '@/utils/util';

/**
 * 大屏统计数据
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/116380
 */

export const fetchScreenStaticItem = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/top/num?shopId=${shopId}`,
		method: 'get',
	});
};

/**
 * 大屏库存品类分布
 *  */

export const fetchCategory = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/middle/categoryNum?shopId=${shopId}`,
		method: 'get',
	});
};
/**
 * 大屏出入库趋势
 *  */
export const fetchInOutTrend = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/middle/inOutTrend?shopId=${shopId}`,
		method: 'get',
	});
};

/**
 * 大屏出入库趋势
 *  */
export const fetchExpiredWarning = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/middle/periodValid?shopId=${shopId}`,
		method: 'get',
	});
};

/**
 * 库存总量排行
 *  */
export const fetchStockNumRank = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/below/stockRanking?shopId=${shopId}`,
		method: 'get',
	});
};
/**
 * 异常报警明细
 *  */
export const fetchAlermDetail = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/below/exceptionDetail?shopId=${shopId}`,
		method: 'get',
	});
};

/**
 * 临期商品数据
 *  */
export const fetchExpiredGoodsDetail = () => {
	const shopId = useShopId();
	return request({
		url: `/wms/data/view/below/adventGoods?shopId=${shopId}`,
		method: 'get',
	});
};

export function goodsStockReport(data) {
	return request({
		url: '/wms/stock/report/goodsStockReport',
		method: 'post',
		data,
	});
}

export function postStockExceptionGoods(data = {}) {
	return request({
		url: '/fresh-wms/stock/report/queryExceptionGoods',
		method: 'post',
		data,
	});
}

export function goodsStockReportCount(data) {
	return request({
		url: '/wms/stock/report/goodsStockReportCount',
		method: 'post',
		data,
	});
}

export function postGoodsStockReportExport(data) {
	return request({
		url: '/fresh-wms/stock/report/download/goodsStockReport',
		method: 'post',
		responseType: 'blob',
		data,
	}).then((res) => {
		downFile(res.data, `进销存台账_${+new Date()}`);
	});
}
