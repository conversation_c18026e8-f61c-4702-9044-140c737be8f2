import request from '/@/utils/request';

/**
 * 收藏菜单
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const collect = (obj: Object) => {
	return request({
		url: '/admin/menu/favorites/collect',
		method: 'post',
		data: obj,
	});
};

/**
 * 取消收藏菜单
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const cancelCollect = (obj: Object) => {
	return request({
		url: '/admin/menu/favorites/cancel',
		method: 'post',
		data: obj,
	});
};

/**
 * 获取收藏菜单
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const getCollectList = () => {
	return request({
		url: '/admin/menu/favorites',
		method: 'get',
	});
};

/**
 * 获取推荐列表
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const getRecommendationList = () => {
	return request({
		url: '/admin/menu/recommendation',
		method: 'get',
	});
};

/**
 * 菜单埋点
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const clickTrack = (obj: Object) => {
	return request({
		url: '/admin/menu/recommendation/clicks',
		method: 'post',
		data: obj,
	});
};

/**
 * 菜单埋点
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const saveSort = (obj: Object) => {
	return request({
		url: '/admin/menu/favorites/sort',
		method: 'post',
		data: obj,
	});
};

/**
 * 取消推荐
 * yapi https://yapi.ops.yunlizhi.cn/project/892/interface/api/72203
 * @param obj
 * @returns
 */
export const cancelRecommendation = (obj: Object) => {
	return request({
		url: '/admin/menu/recommendation/cancel',
		method: 'post',
		data: obj,
	});
};
