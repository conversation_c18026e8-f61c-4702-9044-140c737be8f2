import request from '/@/utils/request';

export function getObj(id) {
	return request({
		url: '/mall/mallconfig',
		method: 'get',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/mallconfig',
		method: 'put',
		data: obj,
	});
}

/**
 * 新增租户配置
 * @param {object} data
 * @returns { code: 0, msg: '', data: {} } Promise
 */
export function saveConfig(data) {
	return request({
		url: '/mall/tenantConfig/saveConfig',
		method: 'post',
		data,
	});
}

/**
 * 查询租户配置
 * @returns { code: 0, msg: '', data: {} } Promise
 */
export function getConfig() {
	return request({
		url: '/mall/tenantConfig/getConfigByTenantId',
		method: 'post',
	});
}

/**
 *
 * 商城/店铺支付配置查询
 */
export function queryPayConfigList(data = {}) {
	return request({
		url: '/mall/payparamconfig/queryPayConfigList',
		method: 'post',
		data,
	});
}

/**
 *
 * 商城支付方式新增/修改
 */
export function payparamconfig(data) {
	return request({
		url: '/mall/payparamconfig',
		method: 'post',
		data,
	});
}

/**
 *
 * 商城支付方式新增/修改
 */
export function getDetail(id) {
	return request({
		url: `/mall/payparamconfig/${id}`,
		method: 'get',
	});
}

/**
 *
 * 店铺配置表新增
 */
export function addConfigs(data) {
	return request({
		url: `/mall/shopconfig/addConfigs`,
		method: 'post',
		data,
	});
}

// 租户货主站点
export function saveStationInfo(data) {
	return request({
		url: '/mall/stationinfo/saveStationInfo',
		method: 'post',
		data,
	});
}

// 货主站点编辑
export function updateStationinfo(data) {
	return request({
		url: '/mall/stationinfo',
		method: 'post',
		data,
	});
}

// 货主站点查询
export function getStationByTenantId(params) {
	return request({
		url: '/mall/stationinfo/getByTenantId',
		method: 'get',
		params,
	});
}
