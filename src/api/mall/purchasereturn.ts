import request from '/@/utils/request';

export function getPage(data) {
	if (data.createTime) {
		data.startTime = data.createTime[0];
		data.endTime = data.createTime[1];
		delete data.createTime;
	}
	return request({
		url: '/wms/purchase_return_order/getPage',
		method: 'post',
		data,
	});
}

export function addObj(obj) {
	return request({
		url: '/wms/purchaseorder/add',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: `/wms/purchase_return_order/getDetail/${id}`,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/purchaseorder/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/purchaseorder',
		method: 'put',
		data: obj,
	});
}

export function submitPurchaseReturnOrder(obj) {
	return request({
		url: '/wms/purchase_return_order/add',
		method: 'post',
		data: obj,
	});
}

export function getSupplierList(data) {
	return request({
		url: '/wms/supplier/getList',
		method: 'post',
		data,
	});
}

export function getWarehouseList(data) {
	return request({
		url: '/wms/warehouseinfo/list',
		method: 'post',
		data,
	});
}

export function getGoodsList(data) {
	return request({
		url: '/mall/goodsselector/page',
		method: 'post',
		data,
	});
}

export function cancelPurchaseOrder(id) {
	return request({
		url: `/wms/purchase_return_order/cancel/${id}`,
		method: 'delete',
	});
}

export function getShopList(data) {
	return request({
		url: '/mall/shopinfo/list',
		method: 'get',
		data,
	});
}

export function getWarehouseListNew(params) {
	return request({
		url: '/wms/warehouseinfo/page',
		method: 'get',
		params,
	});
}
//获取快递公司列表
export function getListByCondition() {
	return request({
		url: '/mall/logistics/listByCondition?enable=1',
		method: 'get',
	});
}

// 选择之后的采退详情接口
export function getSelectedInfo(purchaseOrderNo) {
	return request({
		url: `/wms/purchase_return_order/getPurchaseOrderDetail/${purchaseOrderNo}`,
		method: 'get',
	});
}
