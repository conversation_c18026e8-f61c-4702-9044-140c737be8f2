import request from '/@/utils/request';
import getShopId from '/@/hooks/shopId';

export function getPage(query) {
	return request({
		url: '/mall/goodsspu/page',
		method: 'get',
		params: query,
	});
}

export function getCount(query) {
	return request({
		url: '/mall/goodsspu/count',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/goodsspu',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/goodsspu/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/goodsspu/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/goodsspu',
		method: 'put',
		data: obj,
	});
}

export function putObjShelf(obj) {
	return request({
		url: '/mall/goodsspu/shelf',
		method: 'put',
		params: obj,
	});
}

export function putObjVerify(obj) {
	return request({
		url: '/mall/goodsspu/verify',
		method: 'put',
		params: obj,
	});
}

export function getStatistics(query) {
	return request({
		url: '/mall/goodsspu/statistics',
		method: 'get',
		params: query,
	});
}

export function getListByIds(data) {
	if (data && data.length > 0) {
		return request({
			url: '/mall/goodsspu/listbyids',
			method: 'post',
			data: data,
		});
	} else {
		return new Promise((resolve, reject) => {
			let result = {
				data: {
					data: [],
				},
			};
			resolve(result);
		});
	}
}

export function addConfigs(data) {
	return request({
		url: '/mall/goodsspu/syncGoodsSpuStock',
		method: 'post',
		data,
	});
}

//获取供应商商品列表
export function getSupplierGoodsPage(data) {
	return request({
		url: '/wms/supplier/getList',
		method: 'post',
		data: data,
	});
}

//获取店铺列表并获取第一条数据
export function getShopList() {
	return request({
		url: '/mall/shopinfo/list',
		method: 'get',
	});
}

//获取规格列表
export function getSpecList(data) {
	return request({
		url: '/mall/goodsspec/listWithTenantId',
		method: 'post',
		data,
	});
}

//获取押金品列表
export function getDepositList(id) {
	return request({
		url: `/mall/goodsDeposit/selectDeposit/${id}`,
		method: 'get',
	});
}

/**
 * 筛选条件
 * https://app.apifox.com/project/3068489
 */

export function filterCondition(shopId, params) {
	return request({
		url: `/wms/posSort/queryGoodsList/${shopId}`,
		method: 'get',
		params,
	});
}

/**
 * 商品分类列表查询
 * https://app.apifox.com/project/3068489
 */

export function categoryList(obj) {
	return request({
		url: `/wms/posSort/categoryList`,
		method: 'get',
		params: obj,
	});
}

/**
 * 展示列表分页查询
 *  https://app.apifox.com/project/3068489
 */
export function queryGoodsList(data) {
	for (let key in data) {
		if (data[key] === '') {
			delete data[key];
		}
	}
	const shopId = getShopId();
	if (shopId) {
		data.shopId = shopId;
	}
	return request({
		url: '/wms/sort/queryGoodsList',
		method: 'post',
		data: data,
	});
}

/**
 * 缺货标记
 *  https://app.apifox.com/project/3068489
 */
export function sortLack(sortId) {
	return request({
		url: `/wms/sort/lack/${sortId}`,
		method: 'post',
		data: {},
	});
}

/**
 * 批量缺货标记
 *  https://app.apifox.com/project/3068489
 */
export function sortBatchLack(data) {
	return request({
		url: `/wms/sort/batchLack`,
		method: 'post',
		data: data,
	});
}

/**
 *  一键分拣
 *  https://app.apifox.com/project/3068489
 */
export function sortBatchSort(data) {
	return request({
		url: `/wms/sort/batchSort`,
		method: 'post',
		data: data,
	});
}

/**
 *  一键打印
 *  https://app.apifox.com/project/3068489
 */
export function sortBatchPrint(data) {
	return request({
		url: `/wms/sort/batchPrint`,
		method: 'post',
		data: data,
	});
}

/**
 *  打印/分拣
 *  https://app.apifox.com/project/3068489
 */
export function sortPrintSort(data) {
	return request({
		url: `/wms/sort/printSort`,
		method: 'post',
		data: data,
	});
}

/**
 *  商品分拣进度
 *  https://app.apifox.com/project/3068489
 */
export function sortGoodsProgress(data) {
	return request({
		url: `/wms/sort/sortProgress`,
		method: 'post',
		data: data,
	});
}

/**
 *  客户分拣进度
 *  https://app.apifox.com/project/3068489
 */
export function sortCusSortProgress(data) {
	return request({
		url: `/wms/sort/cusSortProgress`,
		method: 'post',
		data: data,
	});
}

/**
 *  多次分拣
 *  https://app.apifox.com/project/3068489
 */
export function sortMultiPrintSort(data) {
	return request({
		url: `/wms/sort/multiPrintSort`,
		method: 'post',
		data: data,
	});
}

/**
 *  商品分拣详情
 *  https://app.apifox.com/project/3068489
 */
export function sortGoodsProgressDetail(data) {
	return request({
		url: `/wms/sort/sortDetail`,
		method: 'post',
		data: data,
	});
}

/**
 *  客户分拣详情
 *  https://app.apifox.com/project/3068489
 */
export function sortCustProgressDetail(data) {
	return request({
		url: `/wms/sort/cusSortDetail`,
		method: 'post',
		data: data,
	});
}

/**
 *  判断当天是否存在未排线
 *  https://app.apifox.com/project/3068489
 */
export function sortScheduleByDate(data) {
	return request({
		url: `/wms/sort/scheduleByDate`,
		method: 'post',
		data: data,
	});
}

/**
 *  重置
 *  https://app.apifox.com/project/3068489
 */
export function sortReset(sortId) {
	return request({
		url: `/wms/sort/reset/${sortId}`,
		method: 'get',
	});
}

export const fetchGoodsSpuList = () =>
	request({
		url: `/mall/goodsspu/list?shopId=${getShopId()}`,
		method: 'get',
		params: {},
	});
// 导出供应商
export function exportGoodsSpu(data) {
  return request({
    url: '/fresh-mall-admin/goodsspu/exportGoodsSpu',
    method: 'post',
    responseType: 'blob',
    data
  }).then((res) => {
    const downloadFile = `商品明细.xlsx`
    const blob = new Blob([res.data])
    const csvUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = csvUrl
    link.download = downloadFile
    link.click()
  })
}
