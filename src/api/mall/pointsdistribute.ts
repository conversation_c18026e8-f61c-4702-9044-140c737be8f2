import request from '/@/utils/request';

export function getPage(query) {
  return request({
    url: '/mall/pointsdistribute/page',
    method: 'post',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/mall/pointsdistribute/add',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/mall/pointsdistribute/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/mall/pointsdistribute/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/mall/pointsdistribute',
    method: 'put',
    data: obj
  })
}

export function getUserList(data) {
  // if (shopId) {
  //   data.shopId = shopId
  // } else {
  //   delete data.shopId
  // }
  return request({
    url: '/mall/userinfo/list',
    method: 'post',
    data
  })
}

export function batchVerify(data) {
  return request({
    url: '/mall/pointsdistribute/verify',
    method: 'post',
    data
  })
}
