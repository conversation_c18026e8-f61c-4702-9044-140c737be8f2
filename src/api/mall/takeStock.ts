import request from '/@/utils/request';

export function queryPage(data) {
	return request({
		url: '/wms/takeStock/queryPage',
		method: 'post',
		data,
	});
}

export function addItem(data) {
	return request({
		url: '/wms/takeStock/addItem',
		method: 'post',
		data,
	});
}

export function saveList(data) {
	return request({
		url: '/wms/takeStock/saveList',
		method: 'post',
		data,
	});
}

export function update(data) {
	return request({
		url: '/wms/takeStock/update',
		method: 'post',
		data,
	});
}

export function remove(data) {
	const { id } = data;
	return request({
		url: `/wms/takeStock/remove/${id}`,
		method: 'post',
	});
}

export function detail(data) {
	return request({
		url: `/wms/takeStock/detail`,
		method: 'post',
		data,
	});
}

export function audit(data) {
	const { id } = data;
	return request({
		url: `/wms/takeStock/audit/${id}`,
		method: 'get',
	});
}

export function exportExcel(data) {
	const { ignoreZero } = data;
	data.ignoreZero = ignoreZero?.length > 0;
	return request({
		url: '/wms/takeStock/exportExcel',
		method: 'post',
		responseType: 'blob',
		params: data,
	}).then(async (response) => {
		// 获取文件名称
		let defaultName = `${new Date().getTime()}.xlsx`;
		try {
			const disposition = response.headers['content-disposition'];
			defaultName = decodeURIComponent(disposition.split('filename=')[1]);
		} catch (error) {
			console.log(error);
		}
		const data = response.data;
		const type = data?.type;
		const blob = new Blob([data], { type });
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = defaultName;
		document.body.appendChild(link);
		link.click();
		window.setTimeout(function () {
			URL.revokeObjectURL(blob);
			document.body.removeChild(link);
		}, 0);
	});
}

export function importExcel(data) {
	return request({
		url: '/wms/takeStock/importExcel',
		method: 'post',
		responseType: 'blob',
		data,
	}).then((response) => {
		// 判断response.data.type 是excel
		if (response.data.type === 'application/vnd.ms-excel') {
			const blob = response.data;
			const link = document.createElement('a');
			link.href = URL.createObjectURL(blob);
			link.download = `导入结果-${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
			document.body.appendChild(link);
			link.click();
			window.setTimeout(function () {
				URL.revokeObjectURL(blob);
				document.body.removeChild(link);
			}, 0);

			return {
				code: '00500',
				data: null,
				success: false,
				msg: '导入失败',
			};
		}
		return {
			...response,
		};
	});
}

export function getLatestStock(params) {
	return request({
		url: '/wms/goods_stock/getLatestStock',
		method: 'post',
		params,
	});
}
