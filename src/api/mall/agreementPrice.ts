import request from '/@/utils/request';

//分页列表
export function getPageByLine(data) {
  return request({
    url: '/mall/shopAgreementInfo/page',
    method: 'post',
    data
  })
}

export function getPageByOrder(data) {
  return request({
    url: '/mall/shopAgreementGoods/page ',
    method: 'post',
    data
  })
}

//获取客户类型列表
export function getCustomerTypeList(data) {
  return request({
    url: '/mall/shop_customer_category/getList ',
    method: 'post',
    data
  })
}

// 获取协议单详情
export function getAgreementPriceDetail(data) {
  return request({
    url: '/mall/shopAgreementInfo/queryByNo',
    method: 'post',
    data
  })
}

//新增协议价
export function addAgreementPrice(data) {
  return request({
    url: '/mall/shopAgreementInfo/add',
    method: 'post',
    data
  })
}

//编辑协议价
export function updateAgreementPrice(data) {
  return request({
    url: '/mall/shopAgreementInfo/update',
    method: 'post',
    data
  })
}
export function putObj(data) {
  return request({
    url: `/mall/shopAgreementGoods/switchStatus/${data.id}/${data.status}`,
    method: 'get'
  })
}

export function exportExcel(id) {
  return request({
    url: `/mall/shopAgreementCustomer/exportAgreementData/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

export function switchEnable(data) {
  return request({
    url: '/mall/shopAgreementInfo/switchEnable',
    method: 'post',
    data
  })
}

// 按协议价更新价格
export function updateByAgreement(data) {
  return request({
    url: '/mall/shopAgreementInfo/updateByAgreement',
    method: 'post',
    data
  })
}
