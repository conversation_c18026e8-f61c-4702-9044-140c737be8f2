import request from '/@/utils/request';
import { downFile } from '/@/utils/util';

export function getPage(query) {
	return request({
		url: '/mall/orderinfo/orderSummary',
		method: 'post',
		data: query,
	});
}

export function purchaseorderAddGroup(data) {
	return request({
		url: '/wms/purchaseorder/addGroup',
		method: 'post',
		data,
	});
}

export function purchaseorderAddPlan(data) {
	return request({
		url: '/wms/purchasePlanOrder/batchAdd',
		method: 'post',
		data,
	});
}

export function exportSummary(params) {
	return request({
		url: '/mall/orderinfo/exportSummary',
		method: 'get',
		responseType: 'blob',
		params,
	}).then((res) => {
		downFile(res.data, `${params.deliveryTime}订单汇总`);
	});
}

export function getSkuGoodsLevel() {
	return request({
		url: '/mall/goodscategory/tree',
		method: 'get',
	});
}
