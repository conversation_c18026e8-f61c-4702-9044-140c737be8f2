import request from '/@/utils/request';

export function getPage(data) {
  return request({
    url: '/wms/goods_stock_batch/page',
    method: 'post',
    params: data,
    data
  })
}

export function addObj(obj) {
  return request({
    url: '/mall/goodsstockbatch',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/mall/goodsstockbatch/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/mall/goodsstockbatch/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/mall/goodsstockbatch',
    method: 'put',
    data: obj
  })
}
