import request from '/@/utils/request';

/**
 * 销售订单发货
 * https://app.apifox.com/project/2020332/apis/api-123725021
 * @param {*} data
 * @returns
 */
export function getShipSaleOrder(data) {
	return request({
		url: '/wms/sort/shipSaleOrder',
		method: 'post',
		data,
	});
}

/**
 * 一键发货出库
 * https://app.apifox.com/project/2020332/apis/api-123725021
 * @param {*} data
 * @returns
 */
export function batchShip(data) {
	return request({
		url: '/wms/sort/batchShip',
		method: 'post',
		data,
	});
}

/**
 * 发货出库
 * https://app.apifox.com/project/2020332/apis/api-123725021
 * @param {*} data
 * @returns
 */
export function batchShipById(relatioId) {
	return request({
		url: `/wms/sort/ship/${relatioId}`,
		method: 'get',
	});
}

/**
 * 订单导出excel
 * https://app.apifox.com/project/2020332
 */
export function printHandler(data) {
	return request({
		url: '/wms/lineManage/print',
		method: 'post',
		data,
		responseType: 'blob',
	}).then((response) => {
		// 处理返回的文件流
		const data = response.data;
		const href = URL.createObjectURL(new Blob([data], { type: 'application/pdf;charset=utf-8' }));
		return href;
		// window.open(href, 'newWindow');
		// // 获取文件名称
		// let defaultName = `${new Date().getTime()}.xlsx`
		// try {
		//   const disposition = response.headers['content-disposition']
		//   defaultName = decodeURIComponent(disposition.split('filename=')[1])
		// } catch (error) {
		//   console.log(error)
		// }

		// // 处理返回的文件流
		// const type = data?.type
		// const blob = new Blob([data], { type })
		// const link = document.createElement('a')
		// link.href = URL.createObjectURL(blob)
		// link.download = defaultName
		// document.body.appendChild(link)
		// link.click()
		// window.setTimeout(function () {
		//   URL.revokeObjectURL(blob)
		//   document.body.removeChild(link)
		//   window.open(link)
		// }, 0)
	});
}

/**
 * 查询所有线路
 * https://app.apifox.com/project/2020332/apis/api-122190194
 * @param {*} data
 * @returns
 */
export function getLineList(params) {
	return request({
		url: `/wms/lineManage/getLineList`,
		method: 'get',
		params,
	});
}

/**
 * 订单详情(出库列表)
 * https://app.apifox.com/project/2020332/apis/api-122190194
 * @param {*} data
 * @returns
 */
export function shipDetail(data) {
	return request({
		url: '/wms/sort/shipDetail',
		method: 'post',
		data,
	});
}

/**
 * 订单差异列表
 * @param {*} data
 * @returns
 */
export function differenceRecordGetPage(data) {
	return request({
		url: '/wms/differenceRecord/getPage',
		method: 'post',
		data,
	});
}
