import request from '/@/utils/request';

export function getPage(data) {
	return request({
		url: '/mall/shop_customer_info/getPage',
		method: 'post',
		data,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/shopcustomerinfo',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/shopcustomerinfo/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/shopcustomerinfo/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/shopcustomerinfo',
		method: 'put',
		data: obj,
	});
}
