import request from '/@/utils/request';
// 分页列表
export function getPage(data) {
	return request({
		url: '/mall/goodsDeposit/getPage',
		method: 'post',
		data,
	});
}

// 出入库调整接口（入库、出库公用一个接口，type字段区分）
export function adjustStock(data) {
	return request({
		url: '/mall/goodsDeposit/adjustStock',
		method: 'post',
		data,
	});
}

// - 押金品详情---》出入库详情
export function goodsDepositInfo(id) {
	return request({
		url: `/mall/goodsDeposit/getById/${id}`,
		method: 'get',
	});
}

// 获取当前店铺下的所有仓库信息
export function getInfoByShopId(shopId) {
	return request({
		url: `/wms/warehouseinfo/getInfoByShopId/${shopId}`,
		method: 'get',
	});
}

// 获取当前店铺下所有供应商信息
export function getSupplyList(data) {
	return request({
		url: `/wms/supplier/getList`,
		method: 'post',
		data,
	});
}

//新增
export function addObj(data) {
	return request({
		url: '/mall/goodsDeposit/saveOrUpdate',
		method: 'post',
		data,
	});
}

//修改
export function putObj(data) {
	return request({
		url: '/mall/goodsDeposit/saveOrUpdate',
		method: 'post',
		data,
	});
}

//删除

export function delObj(id) {
	return request({
		url: '/mall/goodsDeposit/delete/' + id,
		method: 'delete',
	});
}
