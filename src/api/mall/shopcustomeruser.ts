import request from '/@/utils/request';
export function getPage(data) {
	return request({
		url: '/mall/shop_customer_info/getPage',
		method: 'post',
		data,
	});
}

export function getRelatedPage(data) {
	return request({
		url: '/mall/shop_customer_user/getPage',
		method: 'post',
		data,
	});
}

export function addRelatedUser(obj) {
	return request({
		url: '/mall/shop_customer_user/add',
		method: 'post',
		data: obj,
	});
}

export function delRelatedUser(id) {
	return request({
		url: '/mall/shop_customer_user/' + id,
		method: 'delete',
	});
}

export function addObj(data) {
	return request({
		url: '/mall/shop_customer_info/addOrUpdate',
		method: 'post',
		data,
	});
}

export function getObj(id, shopId) {
	return request({
		url: '/mall/shop_customer_info/getDetail',
		method: 'get',
		params: { id, shopId },
	});
}

// 获取基础信息
export function getBaseInfo(id) {
	return request({
		url: '/mall/shop_customer_info/getDetail/base',
		method: 'get',
		params: { id },
	});
}

// 获取收货信息
export function getReceiveInfo(id) {
	return request({
		url: '/mall/shop_customer_info/getDetail/receive',
		method: 'get',
		params: { id },
	});
}

export function putObj(data) {
	return request({
		url: '/mall/shop_customer_info/addOrUpdate',
		method: 'post',
		data,
	});
}

// 新增/更新收货信息
export function updateReceivingInfo(data) {
	return request({
		url: '/mall/shop_customer_info/addOrUpdate/receive',
		method: 'post',
		data,
	});
}

export function changeStatus(data) {
	return request({
		url: '/mall/shop_customer_info/enable',
		method: 'put',
		data,
	});
}

export function changeStatusOver(obj) {
	return request({
		url: '/mall/shop_customer_info/switch',
		method: 'post',
		data: obj,
	});
}
export function getShopCustomerList(data) {
	return request({
		url: '/mall/shop_customer_info/getList',
		method: 'post',
		data,
	});
}

export function saveReceiveConifg(data) {
	return request({
		url: '/mall/shop_customer_other_info/save',
		method: 'post',
		data,
	});
}

export function getReceiveConfig(shopCustomerId) {
	return request({
		url: `/mall/shop_customer_other_info/${shopCustomerId}`,
		method: 'get',
	});
}


export function getWarehouseList(data) {
  return request({
    url: '/wms/warehouseinfo/list',
    method: 'post',
    data
  })
}
