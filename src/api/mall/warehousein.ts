import request from '/@/utils/request';

export function getPage(data) {
	return request({
		url: '/wms/warehousein/page',
		method: 'post',
		data,
	});
}

export function addObj(obj) {
	return request({
		url: '/wms/warehousein/create',
		method: 'post',
		data: obj,
	});
}

/**
 *
 * 商品期初入库
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/94505
 */
export function postCreateInitial(obj) {
	return request({
		url: '/fresh-wms/warehousein/createInitial',
		method: 'post',
		data: obj,
	});
}

export function getObj(obj) {
	return request({
		url: `/wms/warehousein/detail`,
		method: 'get',
		params: obj,
	});
}

export function delObj(id) {
	return request({
		url: '/wms/warehousein/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/wms/warehousein',
		method: 'put',
		data: obj,
	});
}

export function cancelObj(obj) {
	return request({
		url: '/wms/warehousein/cancel',
		method: 'get',
		params: obj,
	});
}

export function confirmObj(obj) {
	return request({
		url: '/wms/warehousein/confirm',
		method: 'post',
		data: obj,
	});
}
