import request from '/@/utils/request';

export function getPage(data = {}) {
	return request({
		url: '/mall/shop_customer_category/getPage',
		method: 'post',
		data,
	});
}

export function getPlatformPaymentWayList(data = {}) {
	return request({
		url: '/mall/payparamconfig/queryPayConfigList',
		method: 'post',
		data,
	});
}

export function getPaymentWayList(data = {}) {
	return request({
		url: '/mall/payparamconfig/getShopPayMethod',
		method: 'post',
		data,
		// data: Object.assign(data, { shopId: store.state.user?.userInfo?.shopId })
	});
}

export function getList(data) {
	return request({
		url: '/mall/shop_customer_category/getList',
		method: 'post',
		data,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/shop_customer_category/addOrUpdate',
		method: 'post',
		data: obj,
		// data: Object.assign(obj, { shopId: store.state.user?.userInfo?.shopId }),
	});
}

export function getObj(id) {
	return request({
		url: '/mall/shop_customer_category/getDetail',
		method: 'get',
		params: { id },
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/shop_customer_category/addOrUpdate',
		method: 'post',
		data: obj,
	});
}

export function changeStatus(data) {
	return request({
		url: '/mall/shop_customer_category/enable',
		method: 'put',
		params: data,
	});
}
