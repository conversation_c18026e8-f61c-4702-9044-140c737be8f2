/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.jixiansc.com
 * 注意：
 * 本软件为www.jixiansc.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/mall/orderinfo/page',
		method: 'get',
		params: query,
	});
}

export function getUserList(data) {
	return request({
		url: '/mall/shop_customer_user/getListByCustomerId',
		method: 'post',
		data,
	});
}

export function getCount(query) {
	return request({
		url: '/mall/orderinfo/count',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/orderinfo',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/orderinfo/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/orderinfo/' + id,
		method: 'delete',
	});
}

// 编辑订单
export function editOrder(obj) {
	return request({
		url: '/mall/orderinfo/editOrder',
		method: 'put',
		data: obj,
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/orderinfo',
		method: 'put',
		data: obj,
	});
}

export function editPrice(obj) {
	return request({
		url: '/mall/orderinfo/editPrice',
		method: 'put',
		data: obj,
	});
}

export function orderCancel(id) {
	return request({
		url: '/mall/orderinfo/cancel/' + id,
		method: 'put',
	});
}

export function takeGoods(id) {
	return request({
		url: '/mall/orderinfo/takegoods/' + id,
		method: 'put',
	});
}

export function getStatistics(query) {
	return request({
		url: '/mall/orderinfo/statistics',
		method: 'get',
		params: query,
	});
}

export function getStatisticsByColumn(column, query) {
	return request({
		url: '/mall/orderinfo/statistics/' + column,
		method: 'get',
		params: query,
	});
}

export function delivery(obj) {
	return request({
		url: '/mall/orderinfo/deliveryV2',
		method: 'put',
		data: obj,
	});
}

export function getShipmentById(id) {
	return request({
		url: '/mall/orderinfo/shipment/' + id,
		method: 'get',
		// data: obj
	});
}

// 获取订单轨迹地址
export function getTrack(params) {
	return request({
		url: '/mall/ylz/travel/pc',
		method: 'get',
		params,
	});
}

// 是否展示轨迹
export function travelValidate(params) {
	return request({
		url: '/mall/ylz/travel/validate',
		method: 'get',
		params,
	});
}

// 获取用户地址列表
export function getAddressList(params) {
	return request({
		url: '/mall/useraddress/page',
		method: 'get',
		params,
	});
}
// 获取客户地址list
export function getCustomerAddress(id) {
	return request({
		url: `/mall/useraddress/getCustomerAddress/${id}`,
		method: 'get',
	});
}

// 新增用户地址
export function addAddress(data) {
	return request({
		url: '/mall/useraddress',
		method: 'post',
		data,
	});
}

// 代客下音
export function addOrder(data) {
	return request({
		url: '/mall/orderInfoForCustome',
		method: 'post',
		data,
	});
}

/**
 * 订单导出excel
 * https://app.apifox.com/project/2020332
 */
export function exportExcel(params) {
	return request({
		url: '/mall/orderinfo/export',
		method: 'get',
		params,
		responseType: 'blob',
	}).then((response) => {
		// 获取文件名称
		let defaultName = `${new Date().getTime()}.xlsx`;
		try {
			const disposition = response.headers['content-disposition'];
			defaultName = decodeURIComponent(disposition.split('filename=')[1]);
		} catch (error) {
			console.log(error);
		}
		// 处理返回的文件流
		const data = response.data;
		const type = data?.type;
		const blob = new Blob([data], { type });
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = defaultName;
		document.body.appendChild(link);
		link.click();
		window.setTimeout(function () {
			URL.revokeObjectURL(blob);
			document.body.removeChild(link);
		}, 0);
	});
}

/**
 * 通过订单号查询商商城订单detail
 * https://app.apifox.com/project/2020332
 */
export function getDetaiByOrderNo(params) {
	return request({
		url: '/mall/orderinfo/getDetaiByOrderNo',
		method: 'get',
		params,
	});
}

/**
 * 发货
 */
export function deliveryV3(data) {
	return request({
		url: '/mall/orderinfo/deliveryV3',
		method: 'put',
		data: data,
	});
}

export function saveDeliveryV3(data) {
	return request({
		url: '/mall/orderinfo/saveDeliveryV3',
		method: 'put',
		data,
	});
}

// 根据物流单号查询物流公司
export function getLogisticsByNo(params) {
	return request({
		url: '/mall/ylz/transport/kuaidi100Com',
		method: 'get',
		params,
	});
}

/**
 * 订单打印
 */
export function printOrder(params) {
	return request({
		url: '/mall/orderinfo/getPrintDetaiByOrderNo',
		method: 'get',
		params,
	});
}

/**
 * 手动签收
 */
export function signOrder(data) {
	return request({
		url: '/mall/orderinfo/siginOrder',
		method: 'post',
		data,
	});
}

//获取是单店铺还是多店铺
export function getShopMode() {
	return request({
		url: '/admin/tenant/getTenantMode',
		method: 'get',
	});
}

//获取不同状态下的订单数统计
export function countOrderNum() {
	return request({
		url: '/mall/orderinfo/countOrderNum',
		method: 'get',
	});
}

// 查询物流公司
export function getLogisticsList() {
	return request({
		url: '/mall/logistics/list',
		method: 'get',
	});
}

// 识别商品
export function getOcrList(data) {
	return request({
		url: '/mall/goodsselector/getOcrList',
		method: 'post',
		data,
	});
}

export function handleOrder(data) {
	return request({
		url: '/mall/orderinfo/handleOrder',
		method: 'post',
		params: data,
	});
}

export function handleBatchOrder(data) {
	return request({
		url: '/mall/orderinfo/handleOrderBatch',
		method: 'post',
		data,
	});
}

export function saleOrderImport(data) {
	return request({
		url: '/mall/orderInfoForCustome/saleOrderImport',
		method: 'post',
		data,
	});
}

// 获取近5次售价
export function getHistoryPrice(id) {
	return request({
		url: `/fresh-mall-admin/orderitem/querySpuPriceList/${id}`,
		method: 'get',
	});
}

export const fetchPurchaseStock = (data) =>
	request({
		url: '/fresh-wms/goods_stock/queryGoodsSpuStock',
		method: 'post',
		data,
	});

/**
 * 下载批量发货订单
 */
export function exportTemplate(data) {
	return request({
		url: '/mall/orderinfo/exportTemplate',
		method: 'post',
		data,
		responseType: 'blob',
	}).then((response) => {
		// 获取文件名称
		let defaultName = `${new Date().getTime()}.xlsx`;
		try {
			const disposition = response.headers['content-disposition'];
			defaultName = decodeURIComponent(disposition.split('filename=')[1]);
		} catch (error) {
			console.log(error);
		}
		// 处理返回的文件流
		const data = response.data;
		const type = data?.type;
		const blob = new Blob([data], { type });
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = defaultName;
		document.body.appendChild(link);
		link.click();
		window.setTimeout(function () {
			URL.revokeObjectURL(blob);
			document.body.removeChild(link);
		}, 0);
	});
}

export function reverseValidateStock(data) {
	return request({
		url: `/fresh-wms/goods_stock/checkStock/${data}`,
		method: 'post',
	});
}

export function reverseOrder(data) {
	return request({
		url: '/fresh-mall-admin/orderInfoForCustome/reverse',
		method: 'post',
		data,
	});
}

export function getPageTotal(query) {
	return request({
		url: '/mall/orderinfo/page/total',
		method: 'get',
		params: query,
	});
}
