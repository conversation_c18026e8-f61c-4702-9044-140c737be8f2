/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.jixiansc.com
 * 注意：
 * 本软件为www.jixiansc.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/mall/orderrefunds/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/orderrefunds',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/orderrefunds/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/orderrefunds/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/orderrefunds',
		method: 'put',
		data: obj,
	});
}

export function doOrderRefunds(obj) {
	return request({
		url: '/mall/orderrefunds/doOrderRefunds',
		method: 'put',
		data: obj,
	});
}

//通过id获取退款单详情
export function getRefundsDetail(id) {
	return request({
		url: '/mall/orderrefunds/' + id,
		method: 'get',
	});
}

//通过id驳回退款单
export function rejectRefunds(data) {
	return request({
		url: '/mall/orderrefunds/reject',
		method: 'post',
		data,
	});
}

//通过id关闭退款单
export function closeRefunds(data) {
	return request({
		url: '/mall/orderrefunds/close',
		method: 'post',
		data,
	});
}

//退货
export function returnGoods(data) {
	return request({
		url: '/mall/orderrefunds/refund',
		method: 'put',
		data,
	});
}

//退货订单详情
export function getReturnGoodsDetail(params) {
	return request({
		url: '/mall/orderrefunds/refundDetail',
		method: 'post',
		params,
	});
}

export function reverseValidateStock(data) {
	return request({
		url: `/fresh-wms/goods_stock/checkStock/${data}`,
		method: 'post',
	});
}

export function reverseOrder(data) {
	return request({
		url: '/fresh-mall-admin/orderrefunds/reverse',
		method: 'post',
		data,
	});
}
