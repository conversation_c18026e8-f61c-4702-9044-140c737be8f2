import request from '/@/utils/request';

export function getPage(data) {
	return request({
		url: '/wms/warehouseout/page',
		method: 'post',
		data,
	});
}

export function addObj(data) {
	return request({
		url: '/wms/warehouseout/create',
		method: 'post',
		data,
	});
}

export function delObj(params) {
	return request({
		url: '/wms/warehouseout/cancel',
		method: 'get',
		params,
	});
}

export function detailObj(params) {
	return request({
		url: '/wms/warehouseout/detail',
		method: 'get',
		params,
	});
}

export function warehouseoutConfirm(data) {
	return request({
		url: '/wms/warehouseout/confirm',
		method: 'post',
		data,
	});
}

export function getbatchList(data) {
	return request({
		url: `/wms/goods_stock_batch/list?skuId=${data.skuId}&spuId=${data.spuId}`,
		method: 'post',
		data,
	});
}
