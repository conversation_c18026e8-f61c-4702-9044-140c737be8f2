import request from '/@/utils/request';

//请求列表数据
export function getPage(query) {
	return request({
		url: '/mall/signrecord/page',
		method: 'get',
		params: query,
	});
}
//一键分拣
export function sorting(obj) {
	return request({
		url: '/mall/signrecord/sorting',
		method: 'post',
		data: obj,
	});
}

//根据id进行分拣
export function sortingById(obj) {
	return request({
		url: '/mall/signrecord/sortingById',
		method: 'post',
		data: obj,
	});
}

//一键打印标签
export function print(obj) {
	return request({
		url: '/mall/signrecord/print',
		method: 'post',
		data: obj,
	});
}

//根据id进行打印
export function printById(obj) {
	return request({
		url: '/mall/signrecord/printById',
		method: 'post',
		data: obj,
	});
}

export function agreementGoodsImport(obj) {
	return request({
		url: '/mall/shopAgreementInfo/agreementGoodsImport',
		method: 'post',
		data: obj,
	});
}

/**
 * 一键打印分拣单
 */
export function printSortingList(params) {
	return request({
		url: '/wms/sort/batchPrint', //联调替换接口 /mall/sort/printGoodsPick
		method: 'post',
		data: params,
	});
}

/**
 * 线路分拣进度
 */
export const sortProgressOnLine = (params) =>
	request({
		url: '/wms/sort/lineSortList',
		method: 'post',
		data: params,
	});

/**
 * 客户分拣进度
 */
export const sortProgressOnCurstomer = (params) =>
	request({
		url: '/wms/sort/cusSortList',
		method: 'post',
		data: params,
	});

/**
 * 根据线路，客户打印分拣单
 */
export const printPcikListByCustomerOrLine = (params) =>
	request({
		url: '/wms/sort/printCusLinePick',
		method: 'post',
		data: params,
	});

/**
 * 根据线路，客户批量打印标签
 */
export const printTagByCustomerOrLine = (params) =>
	request({
		url: '/wms/sort/cusLinePrintBatch',
		method: 'post',
		data: params,
	});

/**
 * 根据线路，客户批量打印标签
 */
export const sortBatchByCustomerOrLine = (params) =>
	request({
		url: '/wms/sort/cusLineSortBatch',
		method: 'post',
		data: params,
	});
