import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/wms/warehouseinfo/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/wms/warehouseinfo',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/wms/warehouseinfo/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/wms/warehouseinfo/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/wms/warehouseinfo/saveOrUpdate',
		method: 'post',
		data: obj,
	});
}

export function cancelObj(obj) {
	return request({
		url: '/wms/warehouseinfo/changeStatus',
		method: 'post',
		data: obj,
	});
}

export function genWarehouseCode() {
	return request({
		url: '/wms/warehouseinfo/genWarehouseCode',
		method: 'get',
	});
}
