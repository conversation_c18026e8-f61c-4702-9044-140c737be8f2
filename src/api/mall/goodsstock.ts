import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/wms/goods_stock/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/goodsstock',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/goodsstock/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/goodsstock/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/goodsstock',
		method: 'put',
		data: obj,
	});
}

//获取可用库存详情
export function getStockDetail(data) {
	return request({
		url: '/wms/goods_stock/getOccupyStockList',
		method: 'post',
		data,
	});
}

export function saveStockConfig(data) {
	return request({
		url: '/wms/goods_stock/stock/safeQuantity',
		method: 'post',
		data,
	});
}

export const getSpuStockDetail = (data) => {
	return request({
		url: `/wms/goods_stock_batch/page`,
		method: 'post',
		data,
	});
};

export const getSpuStockList = (data) => {
	return request({
		url: `/wms/goods_stock_batch/list`,
		method: 'post',
		data,
	});
};

export function getStockLogPage(data) {
	return request({
		url: '/wms/warehousestockhistorylog/page',
		method: 'post',
		data,
	});
}

export const getGoodsStockInOutDetail = (data) => {
	return request({
		url: `/wms/inout/report/detail/batch`,
		method: 'post',
		data,
	});
};

export function exportGoodsStock(params) {
  return request({
    url: '/wms/goods_stock/page/export',
    method: 'get',
    responseType: 'blob',
    params
  }).then((res) => {
    const downloadFile = `商品库存.xlsx`
    const blob = new Blob([res.data])
    const csvUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = csvUrl
    link.download = downloadFile
    link.click()
  })
}