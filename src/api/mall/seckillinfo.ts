import request from '/@/utils/request';

export function getPage(query) {
	return request({
		url: '/mall/seckillinfo/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj) {
	return request({
		url: '/mall/seckillinfo',
		method: 'post',
		data: obj,
	});
}

export function getObj(id) {
	return request({
		url: '/mall/seckillinfo/' + id,
		method: 'get',
	});
}

export function delObj(id) {
	return request({
		url: '/mall/seckillinfo/' + id,
		method: 'delete',
	});
}

export function putObj(obj) {
	return request({
		url: '/mall/seckillinfo',
		method: 'put',
		data: obj,
	});
}
